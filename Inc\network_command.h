/**
  ******************************************************************************
  * @file    network_command.h
  * @brief   网络下发指令解析和处理模块
  ******************************************************************************
  * @attention
  *
  * 支持的指令格式：
  * ZL+D<数值> - 设备休眠天数设置
  * ZL+H<数值> - 设备休眠小时数设置
  * ZL+M<数值> - 设备休眠分钟数设置
  * ZL+S<数值> - 设备休眠秒数设置
  * ZL+F<开始时间>E<结束时间> - 工作时间段设置，如ZL+F16E22表示16点到22点工作
  * ZL+N<数值> - 触发源切换设置（N0=光敏电阻，N1=触摸）
  * ZL+A<数值> - 速度阈值设置（节 knots），超过阈值触发快发模式
  * ZL+B<数值> - 快发模式下的休眠时间（秒）
  * ZL+T - 发送设备信息（暂时不用）
  *
  ******************************************************************************
  */

#ifndef __NETWORK_COMMAND_H
#define __NETWORK_COMMAND_H

#ifdef __cplusplus
 extern "C" {
#endif

#include "main.h"

// 指令类型定义
typedef enum {
    CMD_TYPE_UNKNOWN = 0,
    CMD_TYPE_SLEEP_DAYS,      // D - 休眠天数
    CMD_TYPE_SLEEP_HOURS,     // H - 休眠小时数
    CMD_TYPE_SLEEP_MINUTES,   // M - 休眠分钟数
    CMD_TYPE_SLEEP_SECONDS,   // S - 休眠秒数
    CMD_TYPE_WORK_TIME,       // F...E - 工作时间段
    CMD_TYPE_PACK_COUNT,      // N - 存储N条数据后再启动GSM模块（已废弃，改为触发源切换）
    CMD_TYPE_TRIGGER_SOURCE,  // N - 触发源切换（N0=光敏电阻，N1=触摸）
    CMD_TYPE_SPEED_THRESHOLD, // A - 速度阈值（km/h）
    CMD_TYPE_FAST_SEND_SLEEP, // B - 快发模式休眠时间（秒）
    CMD_TYPE_SEND_INFO        // T - 发送设备信息（暂时不用）
} NetworkCommand_Type_t;

// 指令解析结果结构体
typedef struct {
    NetworkCommand_Type_t type;
    uint32_t value1;          // 主要数值（休眠时间或开始时间）
    uint32_t value2;          // 次要数值（结束时间，仅用于工作时间段）
    uint8_t is_valid;         // 指令是否有效
} NetworkCommand_Result_t;

// FLASH存储索引定义
#define FLASH_INDEX_SLEEP_TIME      0   // 休眠时间设置（覆盖机制）
#define FLASH_INDEX_WORK_TIME_START 1   // 工作开始时间
#define FLASH_INDEX_WORK_TIME_END   2   // 工作结束时间
#define FLASH_INDEX_PACK_COUNT      3   // 存储N条数据后再启动GSM模块
#define FLASH_INDEX_SPEED_THRESHOLD 4   // 速度阈值（km/h）
#define FLASH_INDEX_FAST_SEND_SLEEP 5   // 快发模式休眠时间（秒）

// 函数声明
HAL_StatusTypeDef NetworkCommand_Parse(const char* command, NetworkCommand_Result_t* result);
HAL_StatusTypeDef NetworkCommand_SaveToFlash(const NetworkCommand_Result_t* result);
HAL_StatusTypeDef NetworkCommand_LoadFromFlash(NetworkCommand_Type_t type, NetworkCommand_Result_t* result);
HAL_StatusTypeDef NetworkCommand_Process(const char* command);
uint8_t NetworkCommand_CheckWorkTime(void);
uint32_t NetworkCommand_CalculateSecondsToWorkTime(uint8_t start_hour, uint8_t end_hour,
                                                   uint8_t current_hour, uint8_t current_minute, uint8_t current_second);
HAL_StatusTypeDef NetworkCommand_UpdateSleepTime(NetworkCommand_Type_t type, uint32_t value);
void NetworkCommand_LoadSettingsOnBoot(void);
uint8_t NetworkCommand_ShouldSkipGSM(void);
void NetworkCommand_CheckSpeedThreshold(float current_speed);
void NetworkCommand_InitLongSleepTracking(uint32_t total_seconds);
uint8_t NetworkCommand_CheckLongSleepStatus(void);
void NetworkCommand_UpdateLongSleepProgress(uint32_t slept_seconds);
void NetworkCommand_ClearLongSleepTracking(void);
void NetworkCommand_PrintResult(const NetworkCommand_Result_t* result);

#ifdef __cplusplus
}
#endif

#endif /* __NETWORK_COMMAND_H */
