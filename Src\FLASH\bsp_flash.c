/**
  ******************************************************************************
  * @file    bsp_flash.c
  * @brief   FLASH read/write operations wrapper
  ******************************************************************************
  */

#include "FLASH/bsp_flash.h"
#include <string.h>
#include <stdio.h>

// ==================== 历史数据存储配置 ====================

// 历史数据存储配置
// STM32L071: 128KB Flash (0x08000000 - 0x0801FFFF)
// 使用后32KB存储历史数据 (96KB-128KB)
#define HISTORY_DATA_START_ADDR     (FLASH_BASE + 0x18000)  // 从96KB开始
#define HISTORY_DATA_RECORD_SIZE    128                       // 每条记录128字节
#define HISTORY_DATA_MAX_RECORDS    200                       // 减少到200条记录，避免超出Flash
#define HISTORY_DATA_TOTAL_SIZE     (HISTORY_DATA_MAX_RECORDS * HISTORY_DATA_RECORD_SIZE)

// 历史数据管理结构（存储在Flash倒数第二页）
typedef struct {
    uint16_t write_index;    // 写入索引（0-199）
    uint16_t read_index;     // 读取索引（0-199）
    uint16_t count;          // 当前数据条数
    uint16_t reserved;       // 保留字段
} HistoryDataInfo_t;

#define HISTORY_INFO_ADDR    (FLASH_BASE + 0x1FF00)  // 使用127.75KB位置存储管理信息

// 静态变量
static HistoryDataInfo_t history_info = {0};
static uint8_t history_initialized = 0;
static char history_backup_buffer[HISTORY_DATA_RECORD_SIZE];  // 全局缓冲区，避免栈溢出

// ==================== Flash基础功能 ====================

static uint8_t flashDataBuffer[FLASH_PAGE_SIZE] = {0};
static uint8_t isFlashDataLoaded = 0;

static HAL_StatusTypeDef Flash_LoadData(void);
static HAL_StatusTypeDef Flash_SaveData(void);

// Initialize FLASH
HAL_StatusTypeDef FLASH_Init(void)
{
  return Flash_LoadData();
}

// Load data from FLASH to buffer
static HAL_StatusTypeDef Flash_LoadData(void)
{
  HAL_StatusTypeDef status;

  status = FLASH_ReadData(flashDataBuffer, FLASH_PAGE_SIZE);
  if (status == HAL_OK)
  {
    isFlashDataLoaded = 1;
  }

  return status;
}

// Save buffer data to FLASH
static HAL_StatusTypeDef Flash_SaveData(void)
{
  return FLASH_WriteData(flashDataBuffer, FLASH_PAGE_SIZE);
}

// Erase FLASH user area
HAL_StatusTypeDef FLASH_EraseUserArea(void)
{
  HAL_StatusTypeDef status = HAL_OK;
  FLASH_EraseInitTypeDef eraseInit;
  uint32_t pageError = 0;
  uint32_t primask;

  primask = __get_PRIMASK();
  __disable_irq();

  status = HAL_FLASH_Unlock();
  if (status != HAL_OK)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return status;
  }

  __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_EOP | FLASH_FLAG_WRPERR | FLASH_FLAG_PGAERR |
                         FLASH_FLAG_SIZERR | FLASH_FLAG_OPTVERR | FLASH_FLAG_RDERR |
                         FLASH_FLAG_FWWERR | FLASH_FLAG_NOTZEROERR);

  eraseInit.TypeErase = FLASH_TYPEERASE_PAGES;
  eraseInit.PageAddress = FLASH_USER_START_ADDR;
  eraseInit.NbPages = 1;

  status = HAL_FLASHEx_Erase(&eraseInit, &pageError);

  HAL_FLASH_Lock();

  if (!primask)
  {
    __enable_irq();
  }

  return status;
}

// Write data to FLASH
HAL_StatusTypeDef FLASH_WriteData(uint8_t *pData, uint16_t DataSize)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t address = FLASH_USER_START_ADDR;
  uint32_t data = 0;
  uint32_t i = 0;
  uint32_t primask;

  if (pData == NULL || DataSize > FLASH_PAGE_SIZE)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  status = FLASH_EraseUserArea();
  if (status != HAL_OK)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return status;
  }

  status = HAL_FLASH_Unlock();
  if (status != HAL_OK)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return status;
  }

  __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_EOP | FLASH_FLAG_WRPERR | FLASH_FLAG_PGAERR |
                         FLASH_FLAG_SIZERR | FLASH_FLAG_OPTVERR | FLASH_FLAG_RDERR |
                         FLASH_FLAG_FWWERR | FLASH_FLAG_NOTZEROERR);

  for (i = 0; i < DataSize; i += 4)
  {
    data = 0;
    if (i < DataSize) data |= ((uint32_t)pData[i]);
    if (i + 1 < DataSize) data |= ((uint32_t)pData[i + 1] << 8);
    if (i + 2 < DataSize) data |= ((uint32_t)pData[i + 2] << 16);
    if (i + 3 < DataSize) data |= ((uint32_t)pData[i + 3] << 24);

    status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, address, data);
    if (status != HAL_OK)
    {
      HAL_FLASH_Lock();

      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }

    address += 4;
  }

  HAL_FLASH_Lock();

  if (!primask)
  {
    __enable_irq();
  }

  return status;
}

// Read data from FLASH
HAL_StatusTypeDef FLASH_ReadData(uint8_t *pData, uint16_t DataSize)
{
  uint32_t address = FLASH_USER_START_ADDR;
  uint32_t data = 0;
  uint32_t i = 0;
  uint32_t primask;

  if (pData == NULL || DataSize > FLASH_PAGE_SIZE)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  for (i = 0; i < DataSize; i += 4)
  {
    data = *(__IO uint32_t *)address;

    if (i < DataSize) pData[i] = (uint8_t)(data & 0xFF);
    if (i + 1 < DataSize) pData[i + 1] = (uint8_t)((data >> 8) & 0xFF);
    if (i + 2 < DataSize) pData[i + 2] = (uint8_t)((data >> 16) & 0xFF);
    if (i + 3 < DataSize) pData[i + 3] = (uint8_t)((data >> 24) & 0xFF);

    address += 4;
  }

  if (!primask)
  {
    __enable_irq();
  }

  return HAL_OK;
}

// Write single data item to FLASH
HAL_StatusTypeDef Flash_Write(uint8_t index, uint8_t dataType, uint32_t data)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t offset;
  uint32_t primask;

  if (index > FLASH_MAX_DATA_INDEX)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  if (!isFlashDataLoaded)
  {
    status = Flash_LoadData();
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }
  }

  offset = index * FLASH_DATA_ITEM_SIZE;

  flashDataBuffer[offset] = dataType;

  switch (dataType)
  {
    case FLASH_DATA_TYPE_UINT8:
      flashDataBuffer[offset + 1] = (uint8_t)(data & 0xFF);
      flashDataBuffer[offset + 2] = 0;
      flashDataBuffer[offset + 3] = 0;
      flashDataBuffer[offset + 4] = 0;
      flashDataBuffer[offset + 5] = 0;
      flashDataBuffer[offset + 6] = 0;
      flashDataBuffer[offset + 7] = 0;
      break;

    case FLASH_DATA_TYPE_UINT16:
      flashDataBuffer[offset + 1] = (uint8_t)(data & 0xFF);
      flashDataBuffer[offset + 2] = (uint8_t)((data >> 8) & 0xFF);
      flashDataBuffer[offset + 3] = 0;
      flashDataBuffer[offset + 4] = 0;
      flashDataBuffer[offset + 5] = 0;
      flashDataBuffer[offset + 6] = 0;
      flashDataBuffer[offset + 7] = 0;
      break;

    case FLASH_DATA_TYPE_UINT32:
    case FLASH_DATA_TYPE_FLOAT:
      flashDataBuffer[offset + 1] = (uint8_t)(data & 0xFF);
      flashDataBuffer[offset + 2] = (uint8_t)((data >> 8) & 0xFF);
      flashDataBuffer[offset + 3] = (uint8_t)((data >> 16) & 0xFF);
      flashDataBuffer[offset + 4] = (uint8_t)((data >> 24) & 0xFF);
      flashDataBuffer[offset + 5] = 0;
      flashDataBuffer[offset + 6] = 0;
      flashDataBuffer[offset + 7] = 0;
      break;

    case FLASH_DATA_TYPE_STRING:
      // For string type, data parameter is not used in this function
      // Use Flash_WriteString instead
      if (!primask)
      {
        __enable_irq();
      }
      return HAL_ERROR;

    default:
      if (!primask)
      {
        __enable_irq();
      }
      return HAL_ERROR;
  }

  status = Flash_SaveData();

  if (!primask)
  {
    __enable_irq();
  }

  return status;
}

// Read single data item from FLASH
HAL_StatusTypeDef Flash_Read(uint8_t index, uint8_t dataType, void *pData)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t offset;
  uint32_t data = 0;
  uint32_t primask;

  if (index > FLASH_MAX_DATA_INDEX || pData == NULL)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  if (!isFlashDataLoaded)
  {
    status = Flash_LoadData();
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }
  }

  offset = index * FLASH_DATA_ITEM_SIZE;

  if (flashDataBuffer[offset] != dataType)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return HAL_ERROR;
  }

  switch (dataType)
  {
    case FLASH_DATA_TYPE_UINT8:
      *((uint8_t *)pData) = flashDataBuffer[offset + 1];
      break;

    case FLASH_DATA_TYPE_UINT16:
      data = (uint32_t)flashDataBuffer[offset + 1] | ((uint32_t)flashDataBuffer[offset + 2] << 8);
      *((uint16_t *)pData) = (uint16_t)data;
      break;

    case FLASH_DATA_TYPE_UINT32:
      data = (uint32_t)flashDataBuffer[offset + 1] |
             ((uint32_t)flashDataBuffer[offset + 2] << 8) |
             ((uint32_t)flashDataBuffer[offset + 3] << 16) |
             ((uint32_t)flashDataBuffer[offset + 4] << 24);
      *((uint32_t *)pData) = data;
      break;

    case FLASH_DATA_TYPE_FLOAT:
      data = (uint32_t)flashDataBuffer[offset + 1] |
             ((uint32_t)flashDataBuffer[offset + 2] << 8) |
             ((uint32_t)flashDataBuffer[offset + 3] << 16) |
             ((uint32_t)flashDataBuffer[offset + 4] << 24);
      *((float *)pData) = *((float *)&data);
      break;

    case FLASH_DATA_TYPE_STRING:
      // For string type, use Flash_ReadString instead
      if (!primask)
      {
        __enable_irq();
      }
      return HAL_ERROR;

    default:
      if (!primask)
      {
        __enable_irq();
      }
      return HAL_ERROR;
  }

  if (!primask)
  {
    __enable_irq();
  }

  return HAL_OK;
}

// Write uint32_t data to FLASH
HAL_StatusTypeDef Flash_WriteUint32(uint8_t index, uint32_t value)
{
  return Flash_Write(index, FLASH_DATA_TYPE_UINT32, value);
}

// Read uint32_t data from FLASH
HAL_StatusTypeDef Flash_ReadUint32(uint8_t index, uint32_t *pValue)
{
  return Flash_Read(index, FLASH_DATA_TYPE_UINT32, pValue);
}

// Write string data to FLASH
HAL_StatusTypeDef Flash_WriteString(uint8_t index, const char *pString)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t offset;
  uint32_t primask;
  uint8_t stringLength;

  if (index > FLASH_MAX_DATA_INDEX || pString == NULL)
  {
    return HAL_ERROR;
  }

  stringLength = strlen(pString);
  if (stringLength > (FLASH_DATA_ITEM_SIZE - 2))  // Reserve 1 byte for type, 1 byte for length
  {
    return HAL_ERROR;  // String too long
  }

  primask = __get_PRIMASK();
  __disable_irq();

  if (!isFlashDataLoaded)
  {
    status = Flash_LoadData();
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }
  }

  offset = index * FLASH_DATA_ITEM_SIZE;

  // Set data type
  flashDataBuffer[offset] = FLASH_DATA_TYPE_STRING;

  // Set string length
  flashDataBuffer[offset + 1] = stringLength;

  // Copy string data (up to 6 bytes)
  for (uint8_t i = 0; i < stringLength && i < 6; i++)
  {
    flashDataBuffer[offset + 2 + i] = pString[i];
  }

  // Clear remaining bytes
  for (uint8_t i = stringLength; i < 6; i++)
  {
    flashDataBuffer[offset + 2 + i] = 0;
  }

  status = Flash_SaveData();

  if (!primask)
  {
    __enable_irq();
  }

  return status;
}

// Read string data from FLASH
HAL_StatusTypeDef Flash_ReadString(uint8_t index, char *pString, uint8_t maxLength)
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t offset;
  uint32_t primask;
  uint8_t stringLength;

  if (index > FLASH_MAX_DATA_INDEX || pString == NULL || maxLength == 0)
  {
    return HAL_ERROR;
  }

  primask = __get_PRIMASK();
  __disable_irq();

  if (!isFlashDataLoaded)
  {
    status = Flash_LoadData();
    if (status != HAL_OK)
    {
      if (!primask)
      {
        __enable_irq();
      }
      return status;
    }
  }

  offset = index * FLASH_DATA_ITEM_SIZE;

  if (flashDataBuffer[offset] != FLASH_DATA_TYPE_STRING)
  {
    if (!primask)
    {
      __enable_irq();
    }
    return HAL_ERROR;
  }

  stringLength = flashDataBuffer[offset + 1];

  // Ensure we don't exceed buffer limits
  if (stringLength >= maxLength)
  {
    stringLength = maxLength - 1;
  }

  if (stringLength > 6)  // Maximum 6 bytes for string data
  {
    stringLength = 6;
  }

  // Copy string data
  for (uint8_t i = 0; i < stringLength; i++)
  {
    pString[i] = flashDataBuffer[offset + 2 + i];
  }

  // Null terminate
  pString[stringLength] = '\0';

  if (!primask)
  {
    __enable_irq();
  }

  return HAL_OK;
}

/**
 * @brief 历史数据系统完整测试函数
 * @param test_count 测试记录条数（1-200）
 * @note 直接测试实际的历史数据存储和读取函数，确保实际功能可靠性
 */
void Flash_TestHistoryDataSystem(uint16_t test_count)
{
    extern char global_data_packet[256];  // 复用全局缓冲区
    uint16_t success_count = 0;
    uint16_t failed_count = 0;
    uint16_t original_count = 0;

    // 限制测试条数
    if (test_count > HISTORY_DATA_MAX_RECORDS) {
        printf("Warning: Test count limited to %d records\r\n", HISTORY_DATA_MAX_RECORDS);
        test_count = HISTORY_DATA_MAX_RECORDS;
    }

    printf("=== History Data System Test ===\r\n");
    printf("Test records: %d\r\n", test_count);
    printf("Using REAL history data functions and storage location\r\n");
    printf("Storage address: 0x%08X\r\n", (unsigned int)HISTORY_DATA_START_ADDR);
    printf("Max capacity: %d records\r\n", HISTORY_DATA_MAX_RECORDS);

    // 1. 初始化历史数据系统
    printf("\n1. Initializing history data system...\r\n");
    Flash_InitHistoryStorage();
    original_count = Flash_GetHistoryDataCount();
    printf("Original history count: %d\r\n", original_count);

    // 2. 清空现有历史数据（可选）
    printf("\n2. Clearing existing history data...\r\n");
    Flash_ClearAllHistoryData();
    printf("History data cleared. Current count: %d\r\n", Flash_GetHistoryDataCount());

    // 3. 使用真实的历史数据保存函数测试写入
    printf("\n3. Testing history data save function...\r\n");
    for (uint16_t i = 0; i < test_count; i++) {
        // 生成测试数据，末尾4位从0000开始计数
        snprintf(global_data_packet, sizeof(global_data_packet),
                "HY105S+119.962044+30.276072+20250908091815+23.6+1+11+0.05+3.87+2.46+1.33+0.0+1001+4.8+27+898523122785240%04d+E", i);

        printf("Saving[%d]: %s\r\n", i, global_data_packet);

        // 使用真实的历史数据保存函数
        HAL_StatusTypeDef save_result = Flash_SaveHistoryData(global_data_packet);
        if (save_result == HAL_OK) {
            success_count++;
            printf("Save[%d]: SUCCESS (S->B conversion applied)\r\n", i);
        } else {
            failed_count++;
            printf("Save[%d]: FAILED\r\n", i);
        }

        HAL_Delay(100);  // 短暂延时
    }

    printf("Save phase completed. Success: %d, Failed: %d\r\n", success_count, failed_count);
    printf("Current history count: %d\r\n", Flash_GetHistoryDataCount());

    // 4. 使用真实的历史数据读取函数测试读取
    printf("\n4. Testing history data read function...\r\n");
    uint16_t read_success = 0;
    uint16_t read_failed = 0;
    uint16_t expected_count = Flash_GetHistoryDataCount();

    printf("Expected to read %d records\r\n", expected_count);

    for (uint16_t i = 0; i < expected_count; i++) {
        // 使用真实的历史数据读取函数
        HAL_StatusTypeDef read_result = Flash_GetNextHistoryData(global_data_packet, sizeof(global_data_packet));

        if (read_result == HAL_OK) {
            printf("Read[%d]: %s\r\n", i, global_data_packet);

            // 验证S是否被正确替换为B
            if (strstr(global_data_packet, "HY105B") != NULL) {
                printf("Read[%d]: SUCCESS (S->B conversion verified)\r\n", i);
                read_success++;
            } else {
                printf("Read[%d]: FAILED (S->B conversion not found)\r\n", i);
                read_failed++;
            }
        } else {
            printf("Read[%d]: FAILED (read function returned error)\r\n", i);
            read_failed++;
            break;
        }

        HAL_Delay(10);  // 短暂延时
    }

    printf("Read phase completed. Success: %d, Failed: %d\r\n", read_success, read_failed);
    printf("Remaining history count: %d\r\n", Flash_GetHistoryDataCount());

    // 5. 测试结果统计
    printf("\n=== History Data System Test Results ===\r\n");
    printf("Test records: %d\r\n", test_count);
    printf("Save success: %d\r\n", success_count);
    printf("Save failed: %d\r\n", failed_count);
    printf("Read success: %d\r\n", read_success);
    printf("Read failed: %d\r\n", read_failed);

    uint16_t total_operations = test_count * 2;  // 保存+读取
    uint16_t total_success = success_count + read_success;
    uint16_t total_failed = failed_count + read_failed;

    printf("Total operations: %d (save + read)\r\n", total_operations);
    printf("Total success: %d\r\n", total_success);
    printf("Total failed: %d\r\n", total_failed);
    printf("Success rate: %.1f%%\r\n", (float)total_success * 100.0f / total_operations);

    if (total_failed == 0) {
        printf("PASS: History Data System Test PASSED!\r\n");
    } else {
        printf("FAIL: History Data System Test FAILED!\r\n");
    }

    printf("Final history count: %d\r\n", Flash_GetHistoryDataCount());
    printf("========================================\r\n");
}

// ==================== 历史数据管理功能 ====================

/**
 * @brief 初始化历史数据存储
 */
void Flash_InitHistoryStorage(void)
{
    if (history_initialized) return;

//    printf("Initializing history storage...\r\n");
//    printf("History data start: 0x%08X\r\n", (unsigned int)HISTORY_DATA_START_ADDR);
//    printf("History info addr: 0x%08X\r\n", (unsigned int)HISTORY_INFO_ADDR);
//    printf("Total size: %d bytes\r\n", HISTORY_DATA_TOTAL_SIZE);

    // 检查地址是否在有效范围内
//    if (HISTORY_INFO_ADDR >= (FLASH_BASE + 0x20000)) {
//        printf("ERROR: History info address exceeds Flash size!\r\n");
//        history_initialized = 1;
//        return;
//    }

    // 尝试从Flash加载历史数据管理信息
//    printf("Attempting to load history info from Flash...\r\n");

    // 读取Flash中的管理信息
    HistoryDataInfo_t *flash_info = (HistoryDataInfo_t*)HISTORY_INFO_ADDR;

    // 检查Flash中的数据是否有效（简单的有效性检查）
    if (flash_info->write_index < HISTORY_DATA_MAX_RECORDS &&
        flash_info->read_index < HISTORY_DATA_MAX_RECORDS &&
        flash_info->count <= HISTORY_DATA_MAX_RECORDS) {

        // Flash中的数据看起来有效，使用它
        history_info.write_index = flash_info->write_index;
        history_info.read_index = flash_info->read_index;
        history_info.count = flash_info->count;
        history_info.reserved = flash_info->reserved;

//        printf("Loaded history info from Flash: Count=%d, Write=%d, Read=%d\r\n",
//               history_info.count, history_info.write_index, history_info.read_index);
    } else {
        // Flash中的数据无效或损坏，使用默认值
        printf("Flash info invalid or corrupted, using default values\r\n");
        history_info.write_index = 0;
        history_info.read_index = 0;
        history_info.count = 0;
        history_info.reserved = 0;
    }

    history_initialized = 1;
//    printf("History storage initialized. Count: %d, Write: %d, Read: %d\r\n",
//           history_info.count, history_info.write_index, history_info.read_index);
}

/**
 * @brief 保存历史数据信息到Flash
 */
static HAL_StatusTypeDef Flash_SaveHistoryInfo(void)
{
    HAL_StatusTypeDef status = HAL_OK;

    HAL_FLASH_Unlock();

    // 清除Flash标志
    __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_EOP | FLASH_FLAG_WRPERR | FLASH_FLAG_PGAERR |
                           FLASH_FLAG_SIZERR | FLASH_FLAG_OPTVERR | FLASH_FLAG_RDERR |
                           FLASH_FLAG_FWWERR | FLASH_FLAG_NOTZEROERR);

    // 擦除信息存储页面
    FLASH_EraseInitTypeDef erase_init;
    uint32_t page_error = 0;

    erase_init.TypeErase = FLASH_TYPEERASE_PAGES;
    erase_init.PageAddress = HISTORY_INFO_ADDR;
    erase_init.NbPages = 1;

    status = HAL_FLASHEx_Erase(&erase_init, &page_error);
    if (status != HAL_OK) {
        HAL_FLASH_Lock();
        return status;
    }

    // 写入历史数据信息（按WORD写入）
    uint32_t *info_data = (uint32_t*)&history_info;
    for (uint32_t i = 0; i < sizeof(HistoryDataInfo_t) / 4; i++) {
        status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD,
                                   HISTORY_INFO_ADDR + i * 4,
                                   info_data[i]);
        if (status != HAL_OK) break;
    }

    HAL_FLASH_Lock();
    return status;
}

/**
 * @brief 保存历史数据到Flash
 * @param data 要保存的数据（S会被替换为B）
 * @return HAL_StatusTypeDef
 */
HAL_StatusTypeDef Flash_SaveHistoryData(const char* data)
{
    if (!history_initialized) {
        Flash_InitHistoryStorage();
    }

    if (data == NULL) return HAL_ERROR;

    // 使用全局缓冲区创建备份数据（将S替换为B）
    strncpy(history_backup_buffer, data, sizeof(history_backup_buffer) - 1);
    history_backup_buffer[sizeof(history_backup_buffer) - 1] = '\0';

    // 查找并替换S为B
    for (int i = 0; history_backup_buffer[i] != '\0'; i++) {
        if (history_backup_buffer[i] == 'S') {
            history_backup_buffer[i] = 'B';
            break;  // 只替换第一个S
        }
    }

    // 计算写入地址
    uint32_t write_addr = HISTORY_DATA_START_ADDR +
                         (history_info.write_index * HISTORY_DATA_RECORD_SIZE);

    HAL_StatusTypeDef status = HAL_OK;
    HAL_FLASH_Unlock();

    // 清除Flash标志
    __HAL_FLASH_CLEAR_FLAG(FLASH_FLAG_EOP | FLASH_FLAG_WRPERR | FLASH_FLAG_PGAERR |
                           FLASH_FLAG_SIZERR | FLASH_FLAG_OPTVERR | FLASH_FLAG_RDERR |
                           FLASH_FLAG_FWWERR | FLASH_FLAG_NOTZEROERR);

    // 擦除当前记录页面
    FLASH_EraseInitTypeDef erase_init;
    uint32_t page_error = 0;

    erase_init.TypeErase = FLASH_TYPEERASE_PAGES;
    erase_init.PageAddress = write_addr;
    erase_init.NbPages = 1;

    status = HAL_FLASHEx_Erase(&erase_init, &page_error);
    if (status != HAL_OK) {
        HAL_FLASH_Lock();
        return status;
    }

    // 按WORD写入数据
    uint32_t data_len = strlen(history_backup_buffer) + 1;
    for (uint32_t j = 0; j < data_len; j += 4) {
        uint32_t word_data = 0;
        if (j < data_len) word_data |= ((uint32_t)history_backup_buffer[j]);
        if (j + 1 < data_len) word_data |= ((uint32_t)history_backup_buffer[j + 1] << 8);
        if (j + 2 < data_len) word_data |= ((uint32_t)history_backup_buffer[j + 2] << 16);
        if (j + 3 < data_len) word_data |= ((uint32_t)history_backup_buffer[j + 3] << 24);

        status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, write_addr + j, word_data);
        if (status != HAL_OK) break;
    }

    HAL_FLASH_Lock();

    if (status == HAL_OK) {
        // 更新索引（添加安全检查）
        history_info.write_index = (history_info.write_index + 1) % HISTORY_DATA_MAX_RECORDS;

        // 如果缓冲区满了，移动读取索引（环形缓冲）
        if (history_info.count >= HISTORY_DATA_MAX_RECORDS) {
            history_info.read_index = (history_info.read_index + 1) % HISTORY_DATA_MAX_RECORDS;
        } else {
            history_info.count++;
        }

        // 安全检查：确保索引在有效范围内
        if (history_info.write_index >= HISTORY_DATA_MAX_RECORDS) {
            history_info.write_index = 0;
            printf("WARNING: write_index overflow, reset to 0\r\n");
        }
        if (history_info.read_index >= HISTORY_DATA_MAX_RECORDS) {
            history_info.read_index = 0;
            printf("WARNING: read_index overflow, reset to 0\r\n");
        }
        if (history_info.count > HISTORY_DATA_MAX_RECORDS) {
            history_info.count = HISTORY_DATA_MAX_RECORDS;
            printf("WARNING: count overflow, reset to max\r\n");
        }

        // 保存历史数据信息
        Flash_SaveHistoryInfo();

        printf("History data saved. Count: %d, Write: %d\r\n",
               history_info.count, history_info.write_index);
    }

    return status;
}

/**
 * @brief 获取下一条历史数据
 * @param data 输出缓冲区
 * @param max_len 缓冲区最大长度
 * @return HAL_StatusTypeDef HAL_OK表示成功，HAL_ERROR表示没有更多数据
 */
HAL_StatusTypeDef Flash_GetNextHistoryData(char* data, uint16_t max_len)
{
    if (!history_initialized) {
        Flash_InitHistoryStorage();
    }

    if (data == NULL || history_info.count == 0) {
        return HAL_ERROR;
    }

    // 计算读取地址
    uint32_t read_addr = HISTORY_DATA_START_ADDR +
                        (history_info.read_index * HISTORY_DATA_RECORD_SIZE);

    // 从Flash读取数据
    memset(data, 0, max_len);
    for (uint16_t i = 0; i < max_len - 1 && i < HISTORY_DATA_RECORD_SIZE - 1; i++) {
        data[i] = *(uint8_t*)(read_addr + i);
        if (data[i] == '\0') break;
    }

    // 更新读取索引
    history_info.read_index = (history_info.read_index + 1) % HISTORY_DATA_MAX_RECORDS;
    history_info.count--;

    // 保存更新的信息
    Flash_SaveHistoryInfo();

    printf("History data read. Remaining: %d, Read: %d\r\n",
           history_info.count, history_info.read_index);

    return HAL_OK;
}

/**
 * @brief 获取历史数据条数
 * @return uint16_t 历史数据条数
 */
uint16_t Flash_GetHistoryDataCount(void)
{
    if (!history_initialized) {
        Flash_InitHistoryStorage();
    }

    return history_info.count;
}

/**
 * @brief 清除所有历史数据
 */
void Flash_ClearAllHistoryData(void)
{
    if (!history_initialized) {
        Flash_InitHistoryStorage();
    }

    history_info.write_index = 0;
    history_info.read_index = 0;
    history_info.count = 0;

    Flash_SaveHistoryInfo();

    printf("All history data cleared.\r\n");
}

/**
 * @brief 打印历史数据存储状态（用于调试）
 */
void Flash_PrintHistoryStatus(void)
{
    if (!history_initialized) {
        Flash_InitHistoryStorage();
    }

    printf("=== History Storage Status ===\r\n");
    printf("Initialized: %s\r\n", history_initialized ? "Yes" : "No");
    printf("Current Count: %d\r\n", history_info.count);
    printf("Write Index: %d\r\n", history_info.write_index);
    printf("Read Index: %d\r\n", history_info.read_index);
    printf("Max Records: %d\r\n", HISTORY_DATA_MAX_RECORDS);
    printf("Record Size: %d bytes\r\n", HISTORY_DATA_RECORD_SIZE);
    printf("Total Size: %d bytes\r\n", HISTORY_DATA_TOTAL_SIZE);
    printf("Storage Usage: %.1f%%\r\n", (float)history_info.count * 100.0f / HISTORY_DATA_MAX_RECORDS);

    // 检查Flash中的管理信息
    HistoryDataInfo_t *flash_info = (HistoryDataInfo_t*)HISTORY_INFO_ADDR;
    printf("Flash Info: Count=%d, Write=%d, Read=%d\r\n",
           flash_info->count, flash_info->write_index, flash_info->read_index);
    printf("=============================\r\n");
}
