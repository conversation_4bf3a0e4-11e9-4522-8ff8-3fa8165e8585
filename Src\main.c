/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "usart.h"
#include "rtc.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "e70_config.h"
#include "GSM.h"
#include "GPS.h"
#include <string.h>
#include <stdio.h>
#include "network_command.h"
#include "globals.h"
#include "FLASH/bsp_flash.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
/* 母设备业务流程控制变量 */
static uint8_t e70_first_init_done = 0;        // E70首次初始化标志
static uint32_t e70_wake_start_time = 0;       // E70唤醒开始时间
static uint8_t e70_wake_retry_count = 0;       // E70唤醒重试次数
static uint16_t saved_battery_voltage = 0;     // 保存的电池电压值
static uint16_t slave_device_id = 0;           // 子设备ID
static uint16_t slave_battery_voltage = 0;     // 子设备电池电压
static uint8_t interrupt_enabled = 1;          // 中断使能标志，1=允许响应，0=禁止响应

// 触发源控制变量
static uint8_t current_trigger_source = TRIGGER_SOURCE_PHOTORESISTOR;  // 当前触发源，默认光敏电阻
/* RTC定时唤醒配置 */
#define ENABLE_RTC_WAKEUP  1                    // 1=启用RTC定时唤醒，0=仅外部触发
#define RTC_WAKEUP_INTERVAL_SECONDS  30         // RTC唤醒间隔：3600秒(1小时)

// 唤醒源识别
typedef enum {
    WAKEUP_SOURCE_UNKNOWN = 0,
    WAKEUP_SOURCE_GPIO,     // 外部GPIO中断唤醒
    WAKEUP_SOURCE_RTC       // RTC定时器唤醒
} WakeupSource_t;

static WakeupSource_t current_wakeup_source = WAKEUP_SOURCE_UNKNOWN;
static volatile uint8_t rtc_wakeup_flag = 0;  // RTC唤醒标志

/* 母设备业务流程状态枚举 */
typedef enum {
    MASTER_STATE_WAKE_UP = 0,           // 唤醒阶段
    MASTER_STATE_E70_WAKE_SLAVE,        // 唤醒子设备阶段
    MASTER_STATE_WAIT_SLAVE_DATA,       // 等待子设备数据阶段
    MASTER_STATE_SEND_ACK,              // 发送确认阶段
    MASTER_STATE_VOLTAGE_CHECK,         // 电压检测阶段（E70完成后）
    MASTER_STATE_CAT1_GPS,              // CAT1 GPS数据获取阶段（预留）
    MASTER_STATE_HELLO_TEST,            // 简单测试阶段
    MASTER_STATE_CAT1_UPLOAD,           // CAT1上传数据阶段（预留）
    MASTER_STATE_PREPARE_SLEEP          // 准备休眠阶段
} MasterState_t;

static MasterState_t current_state = MASTER_STATE_WAKE_UP;

// CAT1模块信息全局变量
char global_ccid[32] = {0};
int8_t global_signal_dbm = -128;

// 历史数据发送状态
typedef enum {
    HISTORY_STATE_IDLE = 0,           // 空闲状态
    HISTORY_STATE_SENDING,            // 正在发送历史数据
    HISTORY_STATE_COMPLETE            // 历史数据发送完成
} HistoryState_t;

//static HistoryState_t history_send_state = HISTORY_STATE_IDLE;

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
// 历史数据管理函数
void PrintHistoryDataStatus(void);

// 触发源控制函数
void SetTriggerSource(uint8_t source);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
uint8_t txmp[6]={0xC0,0x00,0x00,0x18,0x04,0x1C};
uint8_t rxmp[10];
/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART1_UART_Init();
  MX_ADC_Init();
  MX_RTC_Init();
  MX_LPUART1_UART_Init();
  MX_USART4_UART_Init();
  /* USER CODE BEGIN 2 */
  printf("=== STM32L071 Master Device Low Power System ===\r\n");

  // 初始化历史数据存储
  Flash_InitHistoryStorage();
  printf("History data count: %d\r\n", Flash_GetHistoryDataCount());

  // 显示历史数据状态
  PrintHistoryDataStatus();

  // 初始化休眠时间全局变量（默认30秒）
  extern uint32_t user_sleep_seconds;
  if (user_sleep_seconds == 0) {
      user_sleep_seconds = 30;  // 默认30秒
  }

  LED_ON;

  // E70首次初始化（生命周期内只执行一次）
  if (!e70_first_init_done) {
    HAL_GPIO_WritePin(GPIOA, M0_Pin|M1_Pin|M2_Pin|RF_PWR_Pin|CAT1_PWR_Pin, GPIO_PIN_RESET);
    // 使用简化配置函数（只发送配置，不验证）
/**
 * @brief  简化的E70配置初始化（只发送配置，不验证）
 * @param  e70_module_address: E70模块网络地址（用于LoRa组网）
 * @param  channel: 信道 (0-31)
 * @param  power: 发射功率 (0=14dBm, 1=10dBm, 2=7dBm, 3=4dBm)
 * @retval 总是返回成功
 */
    E70_InitializeConfigSimple(E70_MODULE_ADDRESS, 10, 1);
    E70_EnterCommMode();
    e70_first_init_done = 1;  // 标记首次初始化完成
  }

  // 初始化时关闭所有模块电源
  RF_PWR_OFF;   // 关闭E70模块
  CAT1_PWR_OFF; // 关闭CAT1模块

  // 启用GPIO中断 - 仅INT1用于唤醒（INT2已取消）
  HAL_NVIC_SetPriority(EXTI4_15_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(EXTI4_15_IRQn);

  // 启用UART中断 - 确保E70通信正常
  HAL_NVIC_SetPriority(LPUART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(LPUART1_IRQn);
  HAL_NVIC_SetPriority(USART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(USART1_IRQn);

  // 初始化EN_PIN控制2通道切换开关
  SetTriggerSource(TRIGGER_SOURCE_TOUCH);
	//收到N1指令使用触摸触发，收到N0指令使用光敏触发
  /* EN_PIN控制说明：
   * 触发源切换：
   * TRIGGER_SOURCE_PHOTORESISTOR (0): 光敏触发模式
   * TRIGGER_SOURCE_TOUCH (1): 触摸触发模式
   */
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    /* ==================== 母设备业务流程状态机 ==================== */
    /* 初始化为无效值 */
    static MasterState_t last_printed_state = (MasterState_t)255;  //
    if (current_state != last_printed_state) {
        last_printed_state = current_state;
    }

    switch (current_state) {

        case MASTER_STATE_WAKE_UP:   /* 唤醒 */
        {
            // 重置数据变量
            slave_device_id = 0;
            slave_battery_voltage = 0;
            saved_battery_voltage = 0;

            // 根据唤醒源决定流程
            if (current_wakeup_source == WAKEUP_SOURCE_RTC) {
                printf("=== RTC Timer Wakeup ===\r\n");
                printf("Skipping E70 communication, using default slave data\r\n");

                // RTC定时唤醒：跳过E70流程，直接进入电压检测
                current_state = MASTER_STATE_VOLTAGE_CHECK;

                // 清除RTC唤醒标志
                rtc_wakeup_flag = 0;
                current_wakeup_source = WAKEUP_SOURCE_UNKNOWN;

            } else {
                // GPIO外部唤醒：执行完整E70流程
                current_state = MASTER_STATE_E70_WAKE_SLAVE;
                e70_wake_retry_count = 0;

                // 清除GPIO唤醒标志
                current_wakeup_source = WAKEUP_SOURCE_UNKNOWN;
            }
            break;
        }

        case MASTER_STATE_E70_WAKE_SLAVE:   /* E70状态 */
        {
            printf("=== E70 Wake Slave Phase ===\r\n");
            // 重置唤醒重试计数器（关键修复）
            e70_wake_retry_count = 0;
            // 开启E70电源
            E70_EnterCommMode();  // 这会断电重启E70模块，重置所有UART状态
            // 等待E70模块完全稳定（断电重启需要更长时间）
            HAL_Delay(100);  // 增加到1秒延时确保E70完全稳定
            // 重新初始化接收状态变量（与通信测试模式保持一致）
            extern volatile uint16_t uart_rx_index;
            extern volatile uint8_t new_data_received;
            extern volatile uint32_t interrupt_count;
            extern volatile uint8_t single_byte_buffer;
            extern volatile uint8_t uart_rx_buffer[];

            // 完全清理接收缓冲区和状态
            uart_rx_index = 0;
            new_data_received = 0;
            interrupt_count = 0;
            single_byte_buffer = 0;

            // 清理接收缓冲区
            memset((void*)uart_rx_buffer, 0, 100);

            /* 停止任何可能残留的UART接收 */
            HAL_UART_AbortReceive_IT(&hlpuart1);

            /* 清理UART可能残留的错误标志，避免影响后续接收（如 ORE/FE/NE/PE） */
            uint32_t prev_err = HAL_UART_GetError(&hlpuart1);
            if (prev_err != HAL_UART_ERROR_NONE) {
//                printf("[WARN] LPUART1 error before clear: 0x%08lX\r\n", prev_err);
            }
            __HAL_UART_CLEAR_OREFLAG(&hlpuart1);
            __HAL_UART_CLEAR_FEFLAG(&hlpuart1);
            __HAL_UART_CLEAR_NEFLAG(&hlpuart1);
            __HAL_UART_CLEAR_PEFLAG(&hlpuart1);
            HAL_Delay(2); // 稳定一下再启动接收

            /* 重新启动接收 （只通过统一入口启动一次，避免重复）*/
            E70_StartContinuousRx();
            // 发送唤醒信号: FC FD + 55 66 + FD
            printf("Sending wake signal to slave device...\r\n");
            uint8_t wake_packet[] = {0xFC, 0xFD, 0x55, 0x66, 0xFD};
            // 给串口硬件一个短暂稳定时间
            HAL_Delay(20);
            HAL_StatusTypeDef txs = HAL_UART_Transmit(&hlpuart1, wake_packet, sizeof(wake_packet), 200);
            if (txs != HAL_OK) {
//                printf("[ERR] Wake TX failed, status=%d, err=0x%08lX\r\n", txs, HAL_UART_GetError(&hlpuart1));
            } else {
//                printf("[TX] Wake packet sent\r\n");
            }

            // 立即检查一次是否有数据（子设备可能在我们启动接收前就发送了）
            extern volatile uint8_t new_data_received;
            if (new_data_received) {
                // printf("Data already received during startup!\r\n");
            }

            // 适当延时，确保GPIO复用/串口进入稳定态
            HAL_Delay(50);

            e70_wake_start_time = HAL_GetTick();
            current_state = MASTER_STATE_WAIT_SLAVE_DATA;
            break;
        }

        case MASTER_STATE_WAIT_SLAVE_DATA:  /* E70通信 */
        {
            // 使用和测试代码完全相同的接收检查逻辑
            extern volatile uint8_t new_data_received;
            extern volatile uint8_t uart_rx_buffer[];
            extern volatile uint16_t uart_rx_index;
            extern volatile uint8_t single_byte_buffer;

            // 检查是否有完整的数据包（使用与通信测试模式相同的逻辑）
            static uint16_t last_processed_index = 0;
            static uint8_t state_first_entry = 1;

            // 每次进入状态时重置处理索引
            if (state_first_entry) {
                last_processed_index = 0;
                state_first_entry = 0;
//                printf("Reset last_processed_index for new wake cycle\r\n");
            }

            if (uart_rx_index > last_processed_index) {
                // 检查是否包含特定格式的数据包（FA FB 00 01 0C E4 FD）
                // 确保有足够的数据进行检查
                if (uart_rx_index >= 7) {
                    // 修复边界条件：确保能检查到所有可能的7字节数据包位置
                    for (uint16_t i = 0; i <= uart_rx_index - 7; i++) {
                        if (uart_rx_buffer[i] == 0xFA && uart_rx_buffer[i+1] == 0xFB && uart_rx_buffer[i+6] == 0xFD) {
                            // 解析子设备数据
                            slave_device_id = (uart_rx_buffer[i+2] << 8) | uart_rx_buffer[i+3];
                            slave_battery_voltage = (uart_rx_buffer[i+4] << 8) | uart_rx_buffer[i+5];

                            printf("Slave ID: 0x%04X, Battery: %dmV\r\n", slave_device_id, slave_battery_voltage);

                            // 清理缓存并停止接收
                            uart_rx_index = 0;
                            last_processed_index = 0;
                            new_data_received = 0;

                            // 停止接收并进入下一状态
                            E70_StopContinuousRx();
                            state_first_entry = 1;  // 重置标志，为下次唤醒做准备
                            current_state = MASTER_STATE_SEND_ACK;
                            break;  // 立即跳出，进入下一状态
                        }
                    }
                }

                // 如果没有找到完整数据包，更新处理索引
                if (uart_rx_index > 0) {
                    last_processed_index = uart_rx_index;
                }
            }

            // 检查UART状态并确保中断接收正常
            uint32_t state = HAL_UART_GetState(&hlpuart1);
            if (!(state & HAL_UART_STATE_BUSY_RX)) {
                HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&single_byte_buffer, 1);
//                printf("[ALERT] LPUART1 interrupt restarted in state machine\r\n");
            }

            // 检查是否需要发送唤醒信号（延长间隔到1秒，提高容错）
            if ((HAL_GetTick() - e70_wake_start_time) > 1000) {
                e70_wake_retry_count++;
                printf("Wake timeout. Retry count: %d\r\n", e70_wake_retry_count);

                if (e70_wake_retry_count < 20) {
                    // 重新发送唤醒信号
//                    printf("Retrying wake signal...\r\n");
                    uint8_t wake_packet[] = {0xFC, 0xFD, 0x55, 0x66, 0xFD};
                    HAL_UART_Transmit(&hlpuart1, wake_packet, sizeof(wake_packet), 10);
                    e70_wake_start_time = HAL_GetTick();
                } else {
                    // 多次重试后仍无响应，E70数据获取失败
                    printf("E70 communication failed after %d retries\r\n", e70_wake_retry_count);
                    E70_StopContinuousRx();  // 确保停止接收
                    RF_PWR_OFF;

                    // 外部唤醒但E70失败：直接进入休眠（保持原逻辑）
                    state_first_entry = 1;  // 重置标志，为下次唤醒做准备
                    current_state = MASTER_STATE_PREPARE_SLEEP;
                }
            }

            break;
        }

        case MASTER_STATE_SEND_ACK:    /* E70通信（已收到数据） */
        {
//            printf("=== Send ACK Phase ===\r\n");

            // 确保接收已停止，避免收发冲突
            E70_StopContinuousRx();
            HAL_Delay(20);

            // 发送确认信号: FC FD + 77 88 + FD
            uint8_t ack_packet[] = {0xFC, 0xFD, 0x77, 0x88, 0xFD};
            HAL_StatusTypeDef acks = HAL_UART_Transmit(&hlpuart1, ack_packet, sizeof(ack_packet), 1000);
            if (acks == HAL_OK) {
//                printf("[TX] ACK packet sent: FC FD 77 88 FD\r\n");
            } else {
//                printf("[ERR] ACK TX failed, status=%d, err=0x%08lX\r\n", acks, HAL_UART_GetError(&hlpuart1));
            }

            // 给E70模块一点时间把串口数据发射到空口
            HAL_Delay(80);

            // 关闭E70模块电源
            RF_PWR_OFF;

            // 转到电压检测阶段
            current_state = MASTER_STATE_VOLTAGE_CHECK;
            break;
        }

        case MASTER_STATE_VOLTAGE_CHECK:   /* 获取电压 */
        {
						RF_PWR_OFF;
            HAL_Delay(200);

            // 读取电池电压值并保存备用
            saved_battery_voltage = ADC_ReadBatteryVoltage();
            float battery_voltage_v = saved_battery_voltage / 1000.0f;
            printf("Master Battery Voltage: %.3fV (%dmV) - Saved\r\n", battery_voltage_v, saved_battery_voltage);

            // 转到CAT1 GPS阶段
            current_state = MASTER_STATE_CAT1_GPS;
            break;
        }

        case MASTER_STATE_CAT1_GPS:  /* 定位 */
        {
            static uint8_t gps_state_initialized = 0;
            static uint8_t test_initialized = 0;
            static uint8_t state_entry_flag = 1;  // 状态首次进入标志

            // 状态首次进入时重置所有变量
            if (state_entry_flag) {
                printf("=== CAT1 GPS Phase ===\r\n");
                gps_state_initialized = 0;
                test_initialized = 0;
                state_entry_flag = 0;
            }

            // 每次进入状态时只打印一次
            if (!gps_state_initialized) {
                gps_state_initialized = 1;
            }

            // 每次唤醒都需要重新初始化CAT1模块
            if (!test_initialized) {
                printf("CAT1/GPS Mode Started\r\n");

                // 1) 开启CAT1模块电源（CAT1_PWR_Pin控制）
                CAT1_PWR_ON;                 // 拉高CAT1电源开关
                HAL_Delay(3000);             // 等待模块上电稳定

                // 2) 初始化AIR780EG（发送2遍AT唤醒+关闭回显由gsm_init内部完成）
                if (gsm_init() == GSM_OK) {
                    printf("GSM init OK\r\n");
                } else {
                    printf("GSM init FAIL\r\n");
                    // 初始化失败，直接进入休眠
                    current_state = MASTER_STATE_PREPARE_SLEEP;
                    break;
                }

                // 3) 通过AT指令开启GNSS输出（AT+CGNSPWR=1），阻塞等待并打印回复
                if (gsm_gps_power_on() == GSM_OK) {
                    printf("Send AT+CGNSPWR=1 OK\r\n");
                } else {
                    printf("Send AT+CGNSPWR=1 FAIL\r\n");
                    // GPS开启失败，直接进入休眠
                    current_state = MASTER_STATE_PREPARE_SLEEP;
                    break;
                }
                test_initialized = 1;
            }

        // 每1秒查询GPS信息，检测定位成功后进入休眠
        static uint32_t last_query_time = 0;
        static uint32_t gps_start_time = 0;
        uint32_t current_time = HAL_GetTick();

        // 状态重置时重置时间变量
        if (state_entry_flag == 0 && gps_start_time == 0) {
            gps_start_time = current_time;
            last_query_time = 0;
//            printf("GPS timing reset for new cycle\r\n");
        }

        // 计算已运行时间
        uint32_t elapsed_time = (current_time - gps_start_time) / 1000;

        if ((current_time - last_query_time) >= 3000) {
            printf("GPS positioning... %d seconds elapsed\r\n", elapsed_time);

            // 使用封装的GPS查询函数
            GPS_Status_t gps_status = GPS_QueryAIR780EG();

            if (gps_status == GPS_OK && GPS_IsDataReady()) {
                extern GPS_Data_t gps_data;

                printf("=== GPS Information ===\r\n");

                if (gps_data.valid) {
                    // 获取CCID号并保存到全局变量
                    printf("Getting CCID...\r\n");
                    if (gsm_get_ccid(global_ccid) == GSM_OK) {
                        // printf("CCID: %s\r\n", global_ccid);
                    } else {
                        printf("Failed to get CCID\r\n");
                        strcpy(global_ccid, "UNKNOWN");
                    }

                    // 获取信号强度并保存到全局变量
                    uint8_t rssi, ber;
                    printf("Getting signal strength...\r\n");
                    if (gsm_get_signal(&rssi, &ber) == GSM_OK) {
                        global_signal_dbm = (int8_t)rssi;
                    } else {
//                        // printf("Failed to get signal strength\r\n");
                        global_signal_dbm = -128;
                        rssi = 0;
                        ber = 99;
                    }
                    gsm_gps_power_off();

                    // 清理数据准备标志
                    GPS_ClearDataReadyFlag();

                    // 重置状态标志和时间变量，为下次唤醒做准备
                    state_entry_flag = 1;
                    gps_start_time = 0;
                    last_query_time = 0;
                    // 转到简单测试阶段
                    current_state = MASTER_STATE_HELLO_TEST;
                    break;
                } else {
                    printf("GPS still searching satellites... (Count: %d)\r\n", gps_data.satellites);
                }

                GPS_ClearDataReadyFlag();
            } else {
//                printf("GPS query failed, retrying...\r\n");
            }

            last_query_time = current_time;
        }

        /* 定位超时检查（2分钟） */
        if (elapsed_time >= 120) {
            printf("GPS data acquisition timeout (2 minutes). Using default location data...\r\n");

            // 关闭GPS功能
            gsm_gps_power_off();

            // 获取CCID号并保存到全局变量（GPS超时时也需要获取）
            printf("Getting CCID...\r\n");
            if (gsm_get_ccid(global_ccid) == GSM_OK) {
                // printf("CCID: %s\r\n", global_ccid);
            } else {
                printf("Failed to get CCID\r\n");
                strcpy(global_ccid, "UNKNOWN");
            }

            // 获取信号强度并保存到全局变量
            uint8_t rssi, ber;
            printf("Getting signal strength...\r\n");
            if (gsm_get_signal(&rssi, &ber) == GSM_OK) {
                global_signal_dbm = (int8_t)rssi;
            } else {
                global_signal_dbm = -128;
                rssi = 0;
                ber = 99;
            }

            // 清理数据准备标志
            GPS_ClearDataReadyFlag();

            // 重置状态标志和时间变量，为下次唤醒做准备
            state_entry_flag = 1;
            gps_start_time = 0;
            last_query_time = 0;

            // GPS超时，使用默认数据继续执行，转到简单测试阶段
            current_state = MASTER_STATE_HELLO_TEST;
            break;
        }

        HAL_Delay(100);          // 小延时避免过快循环
        break;
        }

        case MASTER_STATE_HELLO_TEST:   /* RTC同步 */
        {
            // RTC同步功能
            printf("=== RTC Sync Phase ===\r\n");

            // 打印同步前的RTC时间
            RTC_PrintCurrentTime("RTC before sync: ");

            // 尝试使用GPS数据同步RTC
            if (GPS_SyncRTC(&gps_data)) {
                printf("RTC sync successful!\r\n");

                // 打印同步后的RTC时间
                RTC_PrintCurrentTime("RTC after sync:  ");
            } else {
                printf("RTC sync failed (GPS data invalid)\r\n");
            }

            printf("=========================\r\n");

            // 调用GPS封装的数据合成函数
            GPS_GenerateHYDataPacket(saved_battery_voltage, slave_device_id, slave_battery_voltage);

            // 转到CAT1上传阶段
            current_state = MASTER_STATE_CAT1_UPLOAD;
            break;
        }

        case MASTER_STATE_CAT1_UPLOAD:     /* CAT1通信 */
        {
            printf("=== CAT1 Upload Phase ===\r\n");

            // 连接服务器
            gsm_status_t connect_status = gsm_connect_default_server();
            if (connect_status == GSM_OK) {
                printf("Server connection initiated\r\n");

                // 等待连接建立（恢复到3秒）
                HAL_Delay(3000);

                // 1. 首先发送实时GPS数据包
                printf("Sending real-time GPS data...\r\n");
                char response[64];

                // 使用全局缓冲区避免栈溢出
                extern char global_data_packet[256];  // 声明全局缓冲区
                char* packet_ptr = GPS_GenerateHYDataPacketString(saved_battery_voltage, slave_device_id, slave_battery_voltage, global_data_packet);

                if (packet_ptr == NULL) {
                    printf("Failed to generate GPS data packet\r\n");
                    CAT1_PWR_OFF;
                    current_state = MASTER_STATE_PREPARE_SLEEP;
                    break;
                }

                printf("Real-time data length: %d bytes\r\n", strlen(packet_ptr));
                const char* realtime_data = packet_ptr;

                gsm_status_t send_status = gsm_send_data(realtime_data, response);
                if (send_status == GSM_OK) {
                    // 2. 实时数据发送成功后，检查并发送历史数据
                    uint16_t history_count = Flash_GetHistoryDataCount();
                    if (history_count > 0) {
                        printf("Found %d history records, sending...\r\n", history_count);

                        // 使用全局缓冲区避免栈溢出
                        extern char global_data_packet[256];  // 复用全局缓冲区
                        uint16_t sent_count = 0;
                        uint16_t failed_count = 0;

                        // 发送所有历史数据
                        while (Flash_GetHistoryDataCount() > 0 && sent_count < 10) { // 限制每次最多发送10条
                            if (Flash_GetNextHistoryData(global_data_packet, sizeof(global_data_packet)) == HAL_OK) {
                                printf("Sending history[%d]: %s\r\n", sent_count, global_data_packet);

                                gsm_status_t history_send_status = gsm_send_data(global_data_packet, response);
                                if (history_send_status == GSM_OK) {
                                    printf("History[%d] sent OK\r\n", sent_count);
                                    sent_count++;
                                } else {
                                    printf("History[%d] send failed, re-saving...\r\n", sent_count);
                                    // 发送失败，重新保存到历史数据
                                    Flash_SaveHistoryData(global_data_packet);
                                    failed_count++;
                                    break; // 停止发送剩余历史数据
                                }

                                HAL_Delay(1000); // 历史数据发送间隔
                            } else {
                                break; // 没有更多历史数据
                            }
                        }

                    } else {

                    }

                } else {
                    printf("Real-time data send failed, saving to history...\r\n");
                    // 实时数据发送失败，保存为历史数据
                    Flash_SaveHistoryData(realtime_data);
                }

                // printf("Closing connection...\r\n");
                gsm_tcp_close();
            } else {
                printf("Server connection failed, saving data to history...\r\n");
                // 连接失败，保存实时数据为历史数据
                extern char global_data_packet[256];
                char* packet_ptr = GPS_GenerateHYDataPacketString(saved_battery_voltage, slave_device_id, slave_battery_voltage, global_data_packet);
                if (packet_ptr != NULL) {
                    Flash_SaveHistoryData(packet_ptr);
                }
            }

            // printf("Shutting down CAT1 module...\r\n");
            CAT1_PWR_OFF;
            current_state = MASTER_STATE_PREPARE_SLEEP;
            break;
        }

        case MASTER_STATE_PREPARE_SLEEP:  /* 进入休眠模式 */
        {
            // 进入休眠模式处理
            goto enter_sleep_mode;
        }

        default:
        {
            printf("ERROR: Unknown state %d\r\n", current_state);
            current_state = MASTER_STATE_PREPARE_SLEEP;
            break;
        }
    }

    // 状态机执行完一个状态后，继续循环执行下一个状态
    continue;

    enter_sleep_mode:
    /* 进入休眠模式处理 */
    {
        // 声明GPIO配置结构体，用于休眠配置
        GPIO_InitTypeDef GPIO_InitStruct = {0};

        // ==================== 进入休眠模式 ====================
        printf("Preparing to enter STOP mode...\r\n");

        // 重新使能中断响应标志，准备下次唤醒
        interrupt_enabled = 1;

#if ENABLE_RTC_WAKEUP
        // 先停止之前的RTC唤醒定时器（如果有的话）
        HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);

        // 清除可能存在的RTC唤醒标志
        __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(&hrtc, RTC_FLAG_WUTF);
        __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();

        // 获取休眠时间：优先使用ZL指令设置的时间，否则使用默认值
        extern uint32_t user_sleep_seconds;
        uint32_t sleep_seconds = (user_sleep_seconds > 0) ? user_sleep_seconds : RTC_WAKEUP_INTERVAL_SECONDS;

        // 限制最大休眠时间为18小时
        const uint32_t MAX_SLEEP_SECONDS = 18 * 3600; // 18小时 = 64800秒
        if (sleep_seconds > MAX_SLEEP_SECONDS) {
            printf("Sleep time limited to maximum: %lu seconds (18 hours)\r\n", MAX_SLEEP_SECONDS);
            sleep_seconds = MAX_SLEEP_SECONDS;
        }

        // 配置RTC唤醒定时器
        if (HAL_RTCEx_SetWakeUpTimer_IT(&hrtc, sleep_seconds-1, RTC_WAKEUPCLOCK_CK_SPRE_16BITS) == HAL_OK) {
            printf("RTC wakeup timer set for %lu seconds\r\n", sleep_seconds);
        } else {
            printf("Failed to set RTC wakeup timer\r\n");
        }
#endif

        HAL_Delay(50); // 让串口输出完成

        /* 关闭所有外部模块电源 */
        RF_PWR_OFF;   // 关闭E70模块
        CAT1_PWR_OFF; // 关闭CAT1模块
        LED_OFF;      // 关闭LED

        /* 停止并禁用UART中断 */
        HAL_UART_AbortReceive_IT(&huart1);
        HAL_UART_AbortReceive_IT(&hlpuart1);
        HAL_UART_AbortReceive_IT(&huart4);
        /* 彻底反初始化LPUART1，避免STOP后硬件状态残留 */
        HAL_UART_DeInit(&hlpuart1);

        /* 完全停止ADC和DMA */
        HAL_ADC_Stop_DMA(&hadc);
        HAL_ADC_Stop(&hadc);
        if(hadc.DMA_Handle != NULL) {
            HAL_DMA_Abort(hadc.DMA_Handle);
        }

        /* 将M0、M1、M2配置为输入模式（避免输出驱动消耗功耗） */
        GPIO_InitStruct.Pin = M0_Pin | M1_Pin | M2_Pin;
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
        GPIO_InitStruct.Pull = GPIO_PULLDOWN;  // 下拉到低电平
        HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

        /* 将LPUART1引脚配置为输入模式 */
        GPIO_InitStruct.Pin = GPIO_PIN_2 | GPIO_PIN_3;  // PA2(TX), PA3(RX)
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
        GPIO_InitStruct.Pull = GPIO_PULLDOWN;  // 下拉到低电平
        HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

        /* 关闭外设时钟 */
        __HAL_RCC_USART1_CLK_DISABLE();
        __HAL_RCC_LPUART1_CLK_DISABLE();
        __HAL_RCC_USART4_CLK_DISABLE();
        __HAL_RCC_ADC1_CLK_DISABLE();
        __HAL_RCC_DMA1_CLK_DISABLE();

        // 确保UART传输完成
        HAL_Delay(50);

        /* 禁用所有中断，准备进入低功耗模式 */
        __disable_irq();

        /* 禁用SysTick中断 */
        SysTick->CTRL &= ~SysTick_CTRL_TICKINT_Msk;

        /* 禁用所有中断，只保留唤醒中断 */
        for (uint8_t i = 0; i < 8; i++) {
            NVIC->ICER[i] = 0xFFFFFFFF;
        }

        /* 确保EXTI4_15中断被启用（INT1和INT2唤醒中断） */
        HAL_NVIC_SetPriority(EXTI4_15_IRQn, 0, 0);
        HAL_NVIC_EnableIRQ(EXTI4_15_IRQn);

#if ENABLE_RTC_WAKEUP
        /* 确保RTC唤醒中断被启用 */
        HAL_NVIC_SetPriority(RTC_IRQn, 0, 0);
        HAL_NVIC_EnableIRQ(RTC_IRQn);

        /* 启用RTC唤醒的EXTI */
        __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_IT();
        __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_RISING_EDGE();
#endif

        /* 清除所有挂起的中断 */
        for (uint8_t i = 0; i < 8; i++) {
            NVIC->ICPR[i] = 0xFFFFFFFF;
        }

        /* 清除PWR标志 */
        __HAL_PWR_CLEAR_FLAG(PWR_FLAG_WU);

        /* 重新启用全局中断，但只有EXTI4_15中断处于活动状态 */
        __enable_irq();

        /* 进入STOP模式，使用低功耗调节器 */
        HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);

        /* ==================== 唤醒后恢复 ==================== */

        // 打开LED表示唤醒开始
        LED_ON;

        // 唤醒后判断唤醒源
        if (rtc_wakeup_flag) {
            printf("*** Device woken up by RTC timer ***\r\n");
            printf("RTC wakeup flag: %d, current_wakeup_source: %d\r\n", rtc_wakeup_flag, current_wakeup_source);
        } else {
            printf("*** Device woken up by external trigger ***\r\n");
            printf("RTC wakeup flag: %d, current_wakeup_source: %d\r\n", rtc_wakeup_flag, current_wakeup_source);
        }

        /* 重新配置系统时钟 - STOP模式后必需 */
        SystemClock_Config();

        /* 重新启用SysTick中断 */
        SysTick->CTRL |= SysTick_CTRL_TICKINT_Msk;

        /* 重新启用必要的外设时钟 */
        __HAL_RCC_USART1_CLK_ENABLE();
        __HAL_RCC_LPUART1_CLK_ENABLE();
        __HAL_RCC_USART4_CLK_ENABLE();
        __HAL_RCC_ADC1_CLK_ENABLE();
        __HAL_RCC_DMA1_CLK_ENABLE();

        /* 重新配置GPIO（恢复正常功能） */
        MX_GPIO_Init();

        // 重新初始化外设
        MX_USART1_UART_Init();
        MX_LPUART1_UART_Init();
        MX_USART4_UART_Init();
        MX_ADC_Init();

        // 重新启用必要的中断
        HAL_NVIC_SetPriority(USART1_IRQn, 3, 0);
        HAL_NVIC_EnableIRQ(USART1_IRQn);
        HAL_NVIC_SetPriority(LPUART1_IRQn, 3, 0);
        HAL_NVIC_EnableIRQ(LPUART1_IRQn);
        // USART4只用于调试输出，不需要中断

        // 确保UART完全重新初始化
        HAL_Delay(50);

        /* 重新启用GPIO中断 */
        HAL_NVIC_SetPriority(EXTI4_15_IRQn, 0, 0);
        HAL_NVIC_EnableIRQ(EXTI4_15_IRQn);

        /* 恢复M0、M1、M2为输出模式 */
        GPIO_InitStruct.Pin = M0_Pin | M1_Pin | M2_Pin;
        GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
        GPIO_InitStruct.Pull = GPIO_NOPULL;
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
        HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

        /* 预置为通信模式（TRANS=001）：M2=0, M1=0, M0=1 */
        HAL_GPIO_WritePin(GPIOA, M2_Pin, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOA, M1_Pin, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOA, M0_Pin, GPIO_PIN_SET);

        /* 恢复触发源设置 */
        SetTriggerSource(current_trigger_source);

        /* LED闪烁表示正在唤醒 */
        LED_TOGGLE;
        HAL_Delay(100);
        LED_TOGGLE;
        HAL_Delay(100);
        LED_TOGGLE;
        HAL_Delay(100);
        LED_ON;

        // printf("*** MASTER WAKE UP from STOP mode ***\r\n");

        /* 重置状态机到唤醒状态 */
        current_state = MASTER_STATE_WAKE_UP;

    } // 结束 enter_sleep_mode 代码块

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI|RCC_OSCILLATORTYPE_LSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLLMUL_4;
  RCC_OscInitStruct.PLL.PLLDIV = RCC_PLLDIV_4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART1|RCC_PERIPHCLK_LPUART1
                              |RCC_PERIPHCLK_RTC;
  PeriphClkInit.Usart1ClockSelection = RCC_USART1CLKSOURCE_PCLK2;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.RTCClockSelection = RCC_RTCCLKSOURCE_LSI;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
  * @brief GPIO外部中断回调函数
  * @param GPIO_Pin: 触发中断的引脚
  * @retval None
  */
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
  // 检查中断使能标志，如果禁止则直接返回
  if (!interrupt_enabled) {
    return;
  }

  if (GPIO_Pin == INT1_Pin)
  {
    current_wakeup_source = WAKEUP_SOURCE_GPIO;  // 标记为GPIO唤醒
    interrupt_enabled = 0;  // 禁止后续触发，直到下次休眠前重新使能
  }
  /* INT2_Pin 中断处理已取消 - 硬件更新后不再使用INT2外部触发 */
}

/**
  * @brief RTC唤醒定时器事件回调函数
  * @param hrtc: RTC句柄指针
  * @retval None
  */
void HAL_RTCEx_WakeUpTimerEventCallback(RTC_HandleTypeDef *hrtc)
{
    rtc_wakeup_flag = 1;
    current_wakeup_source = WAKEUP_SOURCE_RTC;  // 标记为RTC唤醒

    // 清除RTC唤醒标志
    __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(hrtc, RTC_FLAG_WUTF);
    __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();

    // 注意：不要在中断回调中使用printf，可能导致系统崩溃
}



/**
 * @brief 打印历史数据状态
 */
void PrintHistoryDataStatus(void)
{
    uint16_t count = Flash_GetHistoryDataCount();
//    printf("=== History Data Status ===\r\n");
    printf("Total history records: %d\r\n", count);
    printf("Max capacity: 200 records\r\n");
//    printf("Storage usage: %.1f%%\r\n", (float)count * 100.0f / 200.0f);
//    printf("==========================\r\n");
}



/**
 * @brief 设置触发源类型
 * @param source: 触发源类型 (TRIGGER_SOURCE_PHOTORESISTOR 或 TRIGGER_SOURCE_TOUCH)
 */
void SetTriggerSource(uint8_t source)
{
    if (source == TRIGGER_SOURCE_PHOTORESISTOR) {
        EN_OFF;  // 输出低电平，选择光敏电阻触发
        current_trigger_source = TRIGGER_SOURCE_PHOTORESISTOR;
//        printf("Trigger source set to: Photoresistor\r\n");
    } else if (source == TRIGGER_SOURCE_TOUCH) {
        EN_ON;   // 输出高电平，选择触摸触发
        current_trigger_source = TRIGGER_SOURCE_TOUCH;
//        printf("Trigger source set to: Touch\r\n");
    } else {
        printf("Invalid trigger source: %d\r\n", source);
    }
}



/* USER CODE END 4 */

/**
  * @brief  Period elapsed callback in non blocking mode
  * @note   This function is called  when TIM6 interrupt took place, inside
  * HAL_TIM_IRQHandler(). It makes a direct call to HAL_IncTick() to increment
  * a global variable "uwTick" used as application time base.
  * @param  htim : TIM handle
  * @retval None
  */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  /* USER CODE BEGIN Callback 0 */

  /* USER CODE END Callback 0 */
  if (htim->Instance == TIM6)
  {
    HAL_IncTick();
  }
  /* USER CODE BEGIN Callback 1 */

  /* USER CODE END Callback 1 */
}





/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  printf("Error_Handler called! System will restart in 3 seconds...\r\n");

  // 重新启用中断以确保串口输出正常
  __enable_irq();

  // 等待串口输出完成
  HAL_Delay(3000);

  // 系统软件复位
  NVIC_SystemReset();

  // 如果复位失败，禁用中断并进入死循环
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
