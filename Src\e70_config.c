/**
 * @file    e70_config.c
 * @brief   E70 LoRa模块配置实现 - 实际验证可用版本
 * <AUTHOR> Name
 * @date    2025年1月
 * @note    此版本已通过实际硬件测试验证，所有基本功能正常工作
 *
 * 验证结果摘要：
 * ✅ 配置模式5 (101): 完全正常，可读写配置
 * ✅ C1 C1 C1 命令: 成功读取6字节配置数据
 * ✅ C0 写配置命令: 成功写入并立即生效
 * ✅ 模式切换: 断电→设置引脚→上电时序正确
 * ❌ 配置模式3 (011): 无响应，不可用
 * ❌ C3 C3 C3 命令: 此型号不支持版本读取
 *
 * 关键函数：
 * - E70_SetModeWithPowerCycle(): 唯一正确的模式切换方法
 * - E70_WriteSimpleConfig(): 已验证的配置写入函数
 * - HAL_UART_RxCpltCallback(): 简化的中断接收，无打印
 */

#include "e70_config.h"
#include "gpio.h"
#include "usart.h"
#include <string.h>
#include <stdio.h>

// E70配置接收缓存（6字节）
volatile uint8_t e70_config_rx_buffer[6];
volatile uint16_t e70_config_rx_index = 0;

// 大缓存持续接收
volatile uint8_t uart_rx_buffer[30];  // 增大缓存
volatile uint16_t uart_rx_index = 0;
volatile uint8_t new_data_received = 0;
volatile uint8_t single_byte_buffer = 0;

// 兼容性变量
volatile uint8_t data_received_flag = 0;
volatile uint8_t actual_data_length = 0;

// 调试计数器
volatile uint32_t interrupt_count = 0;

volatile uint32_t last_rx_tick = 0;

// 私有函数声明
static HAL_StatusTypeDef E70_SendCommand(uint8_t *cmd, uint8_t cmd_len, uint8_t *response, uint8_t *resp_len, uint32_t timeout);

// 串口重定向支持printf
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE
{
    HAL_UART_Transmit(&huart4, (uint8_t *)&ch, 1, 0xFFFF);
    return ch;
}


/**
 * @brief  初始化E70模块
 * @param  None
 * @retval None
 */
void E70_Init(void)
{
    // GPIO 已在 MX_GPIO_Init() 中完成配置；此处不再重复初始化
    // 模式切换统一使用 E70_SetModeWithPowerCycle()，不在此函数内切换模式
}

/*
 * 注意：E70 模式在上电时锁存，禁止运行时调用 E70_SetMode。请使用 E70_SetModeWithPowerCycle。
 */

/**
 * @brief  设置E70模式并重新上电 (正确的方法)
 * @param  mode: 工作模式
 * @retval None
 */
void E70_SetModeWithPowerCycle(E70_Mode_t mode)
{
    // 1. 断电
    RF_PWR_OFF;
    HAL_Delay(200);

    // 2. 设置模式引脚
    HAL_GPIO_WritePin(M0_GPIO_Port, M0_Pin, (mode & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(M1_GPIO_Port, M1_Pin, (mode & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(M2_GPIO_Port, M2_Pin, (mode & 0x04) ? GPIO_PIN_SET : GPIO_PIN_RESET);

    HAL_Delay(100);  // 让引脚状态稳定

    // 调试：打印当前模式与引脚电平，确认模式锁存前的状态为期望值
    GPIO_PinState m0 = HAL_GPIO_ReadPin(M0_GPIO_Port, M0_Pin);
    GPIO_PinState m1 = HAL_GPIO_ReadPin(M1_GPIO_Port, M1_Pin);
    GPIO_PinState m2 = HAL_GPIO_ReadPin(M2_GPIO_Port, M2_Pin);
//    printf("E70 mode pins set: M2=%d M1=%d M0=%d (mode=%d)\r\n", (int)m2, (int)m1, (int)m0, (int)mode);

    // 3. 上电
    RF_PWR_ON;
    HAL_Delay(200);  // 增加延时确保E70完全启动和稳定
}



/**
 * @brief  发送命令并接收响应
 * @param  cmd: 命令数据
 * @param  cmd_len: 命令长度
 * @param  response: 响应缓冲区
 * @param  resp_len: 响应长度指针
 * @param  timeout: 超时时间
 * @retval HAL_StatusTypeDef
 */
static HAL_StatusTypeDef E70_SendCommand(uint8_t *cmd, uint8_t cmd_len, uint8_t *response, uint8_t *resp_len, uint32_t timeout)
{
    HAL_StatusTypeDef status;

    // 发送命令
    status = HAL_UART_Transmit(&hlpuart1, cmd, cmd_len, timeout);
    if (status != HAL_OK) {
        return status;
    }

    // 接收响应
    status = HAL_UART_Receive(&hlpuart1, response, *resp_len, timeout);
    if (status == HAL_TIMEOUT) {
        *resp_len = 0;
    }

    return status;
}

/**
 * @brief  读取模块配置
 * @param  config: 配置结构体指针
 * @retval HAL_StatusTypeDef
 */
HAL_StatusTypeDef E70_ReadConfig(E70_Config_t *config)
{
    uint8_t cmd[3] = {0xC1, 0xC1, 0xC1};
    uint8_t response[10];  // 增加缓冲区大小
    uint8_t resp_len = 10;
    HAL_StatusTypeDef status;

    status = E70_SendCommand(cmd, sizeof(cmd), response, &resp_len, E70_CONFIG_TIMEOUT);

    if (status == HAL_OK && resp_len > 0) {
        // 打印调试信息
        printf("Config response (%d bytes): ", resp_len);
        for (int i = 0; i < resp_len; i++) {
            printf("0x%02X ", response[i]);
        }
        printf("\r\n");

        // 查找C0标识符
        int config_start = -1;
        for (int i = 0; i < resp_len; i++) {
            if (response[i] == 0xC0) {
                config_start = i;
                break;
            }
        }

        if (config_start >= 0 && (config_start + 5) < resp_len) {
            config->head = response[config_start];
            config->addh = response[config_start + 1];
            config->addl = response[config_start + 2];
            config->sped = response[config_start + 3];
            config->chan = response[config_start + 4];
            config->option = response[config_start + 5];
            printf("Config parsed: ADDH=0x%02X, ADDL=0x%02X, CHAN=%d\r\n",
                   config->addh, config->addl, config->chan & 0x1F);
            return HAL_OK;
        }
    }

    printf("Config read failed or invalid response\r\n");
    return HAL_ERROR;
}

/**
 * @brief  写入模块配置
 * @param  config: 配置结构体指针
 * @param  save: 是否保存到flash (1=保存, 0=临时)
 * @retval HAL_StatusTypeDef
 */
HAL_StatusTypeDef E70_WriteConfig(E70_Config_t *config, uint8_t save)
{
    uint8_t cmd[6];
    uint8_t response[6];
    uint8_t resp_len = 6;

    // 构造写入命令
    cmd[0] = save ? 0xC0 : 0xC2;  // C0=保存到flash, C2=临时设置
    cmd[1] = config->addh;
    cmd[2] = config->addl;
    cmd[3] = config->sped;
    cmd[4] = config->chan;
    cmd[5] = config->option;

    return E70_SendCommand(cmd, sizeof(cmd), response, &resp_len, E70_CONFIG_TIMEOUT);
}

/**
 * @brief  设置模块地址 (假设已经在配置模式)
 * @param  address: 16位地址 (0x0000-0xFFFF)
 * @retval HAL_StatusTypeDef
 */
HAL_StatusTypeDef E70_SetAddress(uint16_t address)
{
    HAL_StatusTypeDef status;
    E70_Config_t config;

    // 读取当前配置
    status = E70_ReadConfig(&config);
    if (status != HAL_OK) {
        return status;
    }

    // 修改地址
    config.addh = (address >> 8) & 0xFF;
    config.addl = address & 0xFF;

    // 写入配置
    status = E70_WriteConfig(&config, 1); // 保存到flash

    return status;
}

/**
 * @brief  设置模块信道 (假设已经在配置模式)
 * @param  channel: 信道号 (0-31)
 * @retval HAL_StatusTypeDef
 */
HAL_StatusTypeDef E70_SetChannel(uint8_t channel)
{
    if (channel > 31) {
        return HAL_ERROR;
    }

    HAL_StatusTypeDef status;
    E70_Config_t config;

    // 读取当前配置
    status = E70_ReadConfig(&config);
    if (status != HAL_OK) {
        return status;
    }

    // 修改信道，保持包长设置不变
    config.chan = (config.chan & 0xE0) | (channel & 0x1F);

    // 写入配置
    status = E70_WriteConfig(&config, 1);

    return status;
}

/**
 * @brief  设置发射功率 (假设已经在配置模式)
 * @param  power: 发射功率
 * @retval HAL_StatusTypeDef
 */
HAL_StatusTypeDef E70_SetTxPower(E70_Power_t power)
{
    HAL_StatusTypeDef status;
    E70_Config_t config;

    // 读取当前配置
    status = E70_ReadConfig(&config);
    if (status != HAL_OK) {
        return status;
    }

    // 修改发射功率，保持其他选项不变
    config.option = (config.option & 0xFC) | (power & 0x03);

    // 写入配置
    status = E70_WriteConfig(&config, 1);

    return status;
}

/**
 * @brief  获取模块版本信息 (假设已经在配置模式)
 * @param  version: 版本信息缓冲区
 * @param  len: 缓冲区长度
 * @retval HAL_StatusTypeDef
 */
HAL_StatusTypeDef E70_GetVersion(uint8_t *version, uint8_t len)
{
    uint8_t cmd[3] = {0xC3, 0xC3, 0xC3};
    uint8_t response[10];
    uint8_t resp_len = 10;
    HAL_StatusTypeDef status;

    status = E70_SendCommand(cmd, sizeof(cmd), response, &resp_len, E70_CONFIG_TIMEOUT);

    if (status == HAL_OK && resp_len > 0) {
        printf("Version response (%d bytes): ", resp_len);
        for (int i = 0; i < resp_len; i++) {
            printf("0x%02X ", response[i]);
        }
        printf("\r\n");

        // 复制到用户缓冲区
        int copy_len = (resp_len < len) ? resp_len : len;
        memcpy(version, response, copy_len);

        return HAL_OK;
    }

    printf("Version read failed\r\n");
    return HAL_ERROR;
}

/**
 * @brief  启动中断接收
 * @param  None
 * @retval None
 */
// E70配置专用接收（6字节固定长度）
void E70_StartConfigRx(void)
{
    e70_config_rx_index = 0;
    memset((void*)e70_config_rx_buffer, 0, sizeof(e70_config_rx_buffer));
    HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)e70_config_rx_buffer, 6);
}

void E70_StopConfigRx(void)
{
    HAL_UART_AbortReceive_IT(&hlpuart1);
}

/**
 * @brief  停止中断接收
 * @param  None
 * @retval None
 */
void E70_StopRx(void)
{
    HAL_UART_AbortReceive_IT(&hlpuart1);
}

/**
 * @brief  打印接收缓存
 * @param  None
 * @retval None
 */
void E70_PrintRxBuffer(void)
{
    printf("=== RX Buffer Dump ===\r\n");
    printf("Total bytes received: %d\r\n", e70_config_rx_index);

    if (e70_config_rx_index > 0) {
        printf("Data: ");
        for (int i = 0; i < e70_config_rx_index; i++) {
            printf("0x%02X ", e70_config_rx_buffer[i]);
        }
        printf("\r\n");
    } else {
        printf("No data received\r\n");
    }
    printf("======================\r\n");
}

/**
 * @brief  解析配置数据
 * @param  None
 * @retval None
 */
void E70_ParseConfigData(void)
{
    if (e70_config_rx_index >= 6 && e70_config_rx_buffer[0] == 0xC0) {
        printf("=== Config Data Analysis ===\r\n");
        printf("Header: 0x%02X\r\n", e70_config_rx_buffer[0]);
        printf("Address: 0x%02X%02X\r\n", e70_config_rx_buffer[1], e70_config_rx_buffer[2]);
        printf("SPED: 0x%02X (Baud: %s, Air: %s)\r\n", uart_rx_buffer[3],
               ((uart_rx_buffer[3] >> 3) & 0x07) == 3 ? "9600" : "Other",
               (uart_rx_buffer[3] & 0x07) == 0 ? "2.5k" : "Other");
        printf("Channel: %d\r\n", uart_rx_buffer[4] & 0x1F);
        printf("Option: 0x%02X (Power: %ddBm)\r\n", uart_rx_buffer[5],
               14 - ((uart_rx_buffer[5] & 0x03) * 3));
        printf("============================\r\n");
    } else if (e70_config_rx_index >= 4 && e70_config_rx_buffer[0] == 0xC3) {
        printf("=== Version Data Analysis ===\r\n");
        printf("Header: 0x%02X\r\n", e70_config_rx_buffer[0]);
        printf("Model: E%d series\r\n", e70_config_rx_buffer[1] == 0x70 ? 70 : e70_config_rx_buffer[1]);
        printf("Version: ");
        for (int i = 2; i < e70_config_rx_index; i++) {
            printf("0x%02X ", e70_config_rx_buffer[i]);
        }
        printf("\r\n============================\r\n");
    }
}

/**
 * @brief  写入简单配置参数
 * @param  address: 模块地址
 * @param  channel: 信道 (0-31)
 * @param  power: 发射功率 (0=14dBm, 1=10dBm, 2=7dBm, 3=4dBm)
 * @retval 是否成功
 */
uint8_t E70_WriteSimpleConfig(uint16_t address, uint8_t channel, uint8_t power)
{
    // 构造配置命令: C0 ADDH ADDL SPED CHAN OPTION
    uint8_t cmd[6];
    cmd[0] = 0xC0;                          // 保存到flash
    cmd[1] = (address >> 8) & 0xFF;         // 地址高字节
    cmd[2] = address & 0xFF;                // 地址低字节
    cmd[3] = 0x18;                          // SPED: 9600bps, 2.5k空中速率
    cmd[4] = (channel & 0x1F) | 0x40;       // 信道 + 64字节包长
    cmd[5] = 0x1C | (power & 0x03);         // 选项 + 功率设置

    // 发送配置命令前确保缓冲区干净
    e70_config_rx_index = 0;
    memset((void*)e70_config_rx_buffer, 0, sizeof(e70_config_rx_buffer));

    E70_StartConfigRx();
    HAL_Delay(100);  // 等待接收启动
    HAL_UART_Transmit(&hlpuart1, cmd, 6, 1000);
    HAL_Delay(100);
    E70_StopConfigRx();

    // 检查E70配置响应（固定6字节）
    // 搜索0xC0头部，可能在任何位置
    int config_found = 0;
    for (int i = 0; i < e70_config_rx_index; i++) {
        if (e70_config_rx_buffer[i] == 0xC0) {
            config_found = 1;
            printf("E70 Config OK (found at offset %d)\r\n", i);
            break;
        }
    }

    if (config_found) {
        return 1;  // 配置成功
    } else {
        printf("E70 Config FAILED - ");
        if (e70_config_rx_index == 0) {
            printf("No response\r\n");
        } else {
            printf("No C0 header found in %d bytes: ", e70_config_rx_index);
            for (int i = 0; i < e70_config_rx_index; i++) {
                printf("%02X ", e70_config_rx_buffer[i]);
            }
            printf("\r\n");
        }
        return 0;  // 配置失败
    }
}

/**
 * @brief  读取并打印当前配置
 * @param  None
 * @retval None
 */
void E70_ReadAndPrintConfig(void)
{
    // 发送读取配置命令 C1 C1 C1
    E70_StartConfigRx();
    uint8_t cmd[] = {0xC1, 0xC1, 0xC1};
    HAL_UART_Transmit(&hlpuart1, cmd, 3, 1000);
    HAL_Delay(100);
    E70_StopConfigRx();

    // 检查并解析响应 - 搜索0xC0头部
    int config_start = -1;
    for (int i = 0; i < e70_config_rx_index; i++) {
        if (e70_config_rx_buffer[i] == 0xC0 && (i + 5) < e70_config_rx_index) {
            config_start = i;
            break;
        }
    }

    if (config_start >= 0) {
        uint16_t address = (uart_rx_buffer[config_start + 1] << 8) | uart_rx_buffer[config_start + 2];
        uint8_t channel = uart_rx_buffer[config_start + 4] & 0x1F;
        uint8_t power_bits = uart_rx_buffer[config_start + 5] & 0x03;

        // 功率对照表：0=14dBm, 1=10dBm, 2=7dBm, 3=4dBm
        uint8_t power_dbm;
        switch(power_bits) {
            case 0: power_dbm = 14; break;
            case 1: power_dbm = 10; break;
            case 2: power_dbm = 7; break;
            case 3: power_dbm = 4; break;
            default: power_dbm = 14; break;
        }

        printf("E70 Config: Addr=0x%04X, Ch=%d, Pwr=%ddBm\r\n",
               address, channel, power_dbm);
    } else {
        printf("E70 Config read failed - received %d bytes\r\n", e70_config_rx_index);
    }
}

// ==================== 通信功能实现 ====================

/**
 * @brief  初始化E70配置（生命周期内只调用一次）
 * @param  e70_module_address: E70模块网络地址（用于LoRa组网）
 * @param  channel: 信道 (0-31)
 * @param  power: 发射功率 (0=14dBm, 1=10dBm, 2=7dBm, 3=4dBm)
 * @retval 是否成功
 */
uint8_t E70_InitializeConfig(uint16_t e70_module_address, uint8_t channel, uint8_t power)
{
    printf("E70 Initialize Start\r\n");

    // 进入配置模式
    E70_SetModeWithPowerCycle(E70_MODE_CONFIG5);
    E70_Init();

    // 写入新配置
    uint8_t power_dbm_values[] = {14, 10, 7, 4};
    uint8_t actual_power = (power < 4) ? power_dbm_values[power] : 14;
    printf("Writing config: Addr=0x%04X, Ch=%d, Pwr=%ddBm\r\n",
           e70_module_address, channel, actual_power);
    uint8_t result = E70_WriteSimpleConfig(e70_module_address, channel, power);
    if (result) {
        // 等待配置生效并验证
        HAL_Delay(100);
        E70_ReadAndPrintConfig();
        printf("E70 Config OK\r\n");
        return 1;
    } else {
        printf("E70 Init FAILED!\r\n");
        return 0;
    }
}

/**
 * @brief  简化的E70配置初始化（只发送配置，不验证）
 * @param  e70_module_address: E70模块网络地址（用于LoRa组网）
 * @param  channel: 信道 (0-31)
 * @param  power: 发射功率 (0=14dBm, 1=10dBm, 2=7dBm, 3=4dBm)
 * @retval 总是返回成功
 */
uint8_t E70_InitializeConfigSimple(uint16_t e70_module_address, uint8_t channel, uint8_t power)
{
//    printf("=== E70 Simple Config Start ===\r\n");

    // 进入配置模式
    E70_SetModeWithPowerCycle(E70_MODE_CONFIG5);
    E70_Init();

    // 打印配置参数
//    uint8_t power_dbm_values[] = {14, 10, 7, 4};
//    uint8_t actual_power = (power < 4) ? power_dbm_values[power] : 14;
//    printf("E70 Config Parameters:\r\n");
//    printf("  Address: 0x%04X\r\n", e70_module_address);
//    printf("  Channel: %d\r\n", channel);
//    printf("  Power: %d dBm\r\n", actual_power);
//    printf("  Baud Rate: 9600 bps\r\n");
//    printf("  Air Rate: 2.5k bps\r\n");
//    printf("  Packet Size: 64 bytes\r\n");

    // 构造并发送配置命令
    uint8_t cmd[6];
    cmd[0] = 0xC0;                          // 保存到flash
    cmd[1] = (e70_module_address >> 8) & 0xFF;  // 地址高字节
    cmd[2] = e70_module_address & 0xFF;         // 地址低字节
    cmd[3] = 0x18;                          // SPED: 9600bps, 2.5k空中速率
    cmd[4] = (channel & 0x1F) | 0x40;       // 信道 + 64字节包长
    cmd[5] = 0x1C | (power & 0x03);         // 选项 + 功率设置

//    printf("Sending config command: ");
//    for (int i = 0; i < 6; i++) {
//        printf("0x%02X ", cmd[i]);
//    }
//    printf("\r\n");

    // 发送配置命令（不等待响应）
    HAL_UART_Transmit(&hlpuart1, cmd, 6, 1000);
    HAL_Delay(200);  // 等待配置生效

//    printf("E70 Config Sent Successfully\r\n");
//    printf("=== E70 Simple Config Complete ===\r\n");

    return 1;  // 总是返回成功
}

/**
 * @brief  进入连续通信模式
 * @param  None
 * @retval 是否成功
 */
uint8_t E70_EnterCommMode(void)
{
    E70_SetModeWithPowerCycle(E70_MODE_TRANS);
//    printf("E70 Comm Mode Ready\r\n");
    return 1;
}

/**
 * @brief  发送设备信息
 * @param  mcu_device_id: 单片机设备ID（用于业务逻辑识别）
 * @param  battery_voltage: 电池电压
 * @retval 是否成功
 */
uint8_t E70_SendDeviceInfo(uint16_t mcu_device_id, uint16_t battery_voltage)
{
    E70_SendPacket_t packet;

    // 构造数据包
    packet.header1 = E70_SEND_HEADER1;          // 0xFA
    packet.header2 = E70_SEND_HEADER2;          // 0xFB
    // 手动转换为大端序（高位在前）
    packet.mcu_device_id = (mcu_device_id >> 8) | (mcu_device_id << 8);
    packet.battery_voltage = (battery_voltage >> 8) | (battery_voltage << 8);
    packet.end = E70_PACKET_END;                // 0xFD

    // 发送数据包
    HAL_StatusTypeDef status = HAL_UART_Transmit(&hlpuart1, (uint8_t*)&packet, E70_SEND_PACKET_SIZE, 1000);

    if (status == HAL_OK) {
        printf("Sent: MCU_ID=0x%04X, Volt=%dmV\r\n", mcu_device_id, battery_voltage);
        return 1;
    } else {
        printf("Send Failed\r\n");
        return 0;
    }
}

/**
 * @brief  检查是否收到ACK回复
 * @param  None
 * @retval 是否收到正确ACK
 */
uint8_t E70_CheckReceiveAck(void)
{
    // 检查是否有足够的数据进行5字节ACK包检测
    if (uart_rx_index >= 5) {
        // 在缓冲区中搜索完整的5字节ACK包 (FC FD 55 66 FD)
        for (uint16_t i = 0; i <= uart_rx_index - 5; i++) {
            if (uart_rx_buffer[i] == E70_RECV_HEADER1 &&      // 0xFC
                uart_rx_buffer[i+1] == E70_RECV_HEADER2 &&    // 0xFD
                uart_rx_buffer[i+2] == E70_RECV_ACK1 &&       // 0x55
                uart_rx_buffer[i+3] == E70_RECV_ACK2 &&       // 0x66
                uart_rx_buffer[i+4] == E70_PACKET_END) {      // 0xFD

                printf("ACK Received\r\n");
                return 1;
            }
        }
    }
    return 0;
}

/**
 * @brief  检查是否收到子设备数据并解析
 * @param  device_id: 输出参数，子设备ID
 * @param  battery_voltage: 输出参数，子设备电池电压
 * @retval 是否收到正确的子设备数据
 */
uint8_t E70_CheckReceiveSlaveData(uint16_t *device_id, uint16_t *battery_voltage)
{
    // 检查是否有足够的数据进行7字节数据包检测
    if (uart_rx_index >= 7) {
        // 在缓冲区中搜索完整的7字节数据包 (FA FB xx xx xx xx FD)
        for (uint16_t i = 0; i <= uart_rx_index - 7; i++) {
            if (uart_rx_buffer[i] == E70_SEND_HEADER1 &&      // 0xFA
                uart_rx_buffer[i+1] == E70_SEND_HEADER2 &&    // 0xFB
                uart_rx_buffer[i+6] == E70_PACKET_END) {      // 0xFD

                // 解析设备ID (大端序)
                *device_id = (uart_rx_buffer[i+2] << 8) | uart_rx_buffer[i+3];

                // 解析电池电压 (大端序)
                *battery_voltage = (uart_rx_buffer[i+4] << 8) | uart_rx_buffer[i+5];

                printf("Slave Data Received: ID=0x%04X, Voltage=%dmV\r\n", *device_id, *battery_voltage);

                return 1;
            }
        }
    }
    return 0;
}

/**
 * @brief  启动连续接收模式
 * @param  None
 * @retval None
 */
void E70_StartContinuousRx(void)
{
    // 重置计数器
    interrupt_count = 0;

    // 直接启动单字节接收，不做任何检查
    HAL_StatusTypeDef status = HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&single_byte_buffer, 1);
    printf("StartContinuousRx: status=%d\r\n", status);
}

/**
 * @brief  停止连续接收模式
 * @param  None
 * @retval None
 */
void E70_StopContinuousRx(void)
{
    HAL_UART_AbortReceive_IT(&hlpuart1);
}

// ==================== ADC电池电压采样功能实现 ====================

/**
 * @brief  读取电池电压ADC值
 * @param  None
 * @retval 电池电压(mV)
 */
uint16_t ADC_ReadBatteryVoltage(void)
{
    extern ADC_HandleTypeDef hadc;
    uint32_t adc_buffer[ADC_SAMPLE_COUNT * 2];  // 2通道采样缓冲区：CH0 + VREFINT
    uint32_t battery_sum = 0;
    uint32_t vrefint_sum = 0;
    uint16_t battery_voltage_mv = 0;

    // 清空缓冲区
    memset(adc_buffer, 0, sizeof(adc_buffer));

    // 校准ADC
    HAL_ADCEx_Calibration_Start(&hadc, ADC_SINGLE_ENDED);
    // 启动DMA采样
    if (HAL_ADC_Start_DMA(&hadc, adc_buffer, ADC_SAMPLE_COUNT * 2) == HAL_OK) {
        HAL_Delay(100);  // 等待采样完成
        // 停止ADC
        HAL_ADC_Stop_DMA(&hadc);

        // 分离通道数据并计算平均值
        // 通道顺序：ADC_CHANNEL_0(电池电压,PA0) -> ADC_CHANNEL_VREFINT
        // 注意：如果硬件连接是PA6，需要在CubeMX中修改为ADC_CHANNEL_6
        for (int i = 0; i < ADC_SAMPLE_COUNT * 2; i += 2) {
            battery_sum += adc_buffer[i];      // ADC_CHANNEL_0 电池电压 (PA0)
            vrefint_sum += adc_buffer[i + 1];  // ADC_CHANNEL_VREFINT 内部参考电压
        }
        // 计算平均值
        uint16_t battery_raw = battery_sum / ADC_SAMPLE_COUNT;
        uint16_t vrefint_raw = vrefint_sum / ADC_SAMPLE_COUNT;

        // 使用VREFINT校正计算实际VDD和电池电压
        if (vrefint_raw > 0) {
            // 计算实际VDD (V)
            float actual_vdd = (float)(ADC_VREFINT_CAL_VREF * ADC_VREFINT_CAL_VALUE) / vrefint_raw / 1000.0f;

            // 计算电池电压（考虑2:1分压电阻）
            float voltage = (battery_raw * actual_vdd / 4096.0f) * 2.0f;

            // 应用用户校准系数和偏移量
            voltage = voltage * BATTERY_VOLTAGE_CALIBRATION_FACTOR + BATTERY_VOLTAGE_OFFSET;

            // 转换为mV
            battery_voltage_mv = (uint16_t)(voltage * 1000);
            // 简化调试输出用于校准
            // printf("ADC: Raw=%d, VRef=%d, VDD=%.3fV\r\n", battery_raw, vrefint_raw, actual_vdd);
        } else {
            printf("VREFINT failed, using fixed VDD\r\n");
            // VREFINT失效时使用固定VDD
            float voltage = (battery_raw * 3.3f / 4096.0f) * 2.0f;
            voltage = voltage * BATTERY_VOLTAGE_CALIBRATION_FACTOR + BATTERY_VOLTAGE_OFFSET;
            battery_voltage_mv = (uint16_t)(voltage * 1000);
        }

    } else {
        printf("ADC sampling failed\r\n");
        battery_voltage_mv = 0;
    }

    return battery_voltage_mv;
}

/**
 * @brief  将mV值转换为V值（兼容性函数）
 * @param  voltage_mv: 电压值(mV)
 * @retval 电池电压值(V)
 */
float ADC_ConvertToVoltage(uint16_t voltage_mv)
{
    return voltage_mv / 1000.0f;
}

/**
 * @brief  UART接收完成中断回调函数
 * @param  huart: UART句柄指针
 * @retval None
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == LPUART1) {
        // 增加中断计数器
        interrupt_count++;

        // 存入缓存，不清除，持续累积
        if (uart_rx_index < 100) {
            uart_rx_buffer[uart_rx_index] = single_byte_buffer;
            uart_rx_index++;

            // 注释掉printf调试信息，避免在中断中使用printf导致数据丢失
            // printf("UART RX[%d]: 0x%02X (interrupt_count=%lu)\r\n",
            //        uart_rx_index-1, single_byte_buffer, interrupt_count);
        } else {
            // printf("UART RX buffer overflow! index=%d\r\n", uart_rx_index);
        }

        // 设置接收标志
        new_data_received = 1;

        // 继续接收下一个字节
        HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&single_byte_buffer, 1);

        last_rx_tick = HAL_GetTick();
    } else if (huart->Instance == USART1) {
        // USART1 支持：如果启用了USART1中断接收，则也继续接收
        extern volatile uint8_t usart1_rx_byte;
        extern volatile uint8_t usart1_rx_buffer[];
        extern volatile uint16_t usart1_rx_index;
        extern volatile uint32_t usart1_interrupt_count;
        extern volatile uint32_t usart1_last_rx_tick;
        usart1_interrupt_count++;
        if (usart1_rx_index < 128) {
            usart1_rx_buffer[usart1_rx_index++] = usart1_rx_byte;
        }
        usart1_last_rx_tick = HAL_GetTick();
        HAL_UART_Receive_IT(&huart1, (uint8_t*)&usart1_rx_byte, 1);
    }
}

/**
 * @brief  打印接收到的原始数据（用于通信测试）
 * @param  None
 * @retval None
 */
void E70_PrintReceivedData(void)
{
    // 这个函数现在不需要了，在main中直接处理
}
