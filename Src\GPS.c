/**
  ******************************************************************************
  * @file    GPS.c
  * @brief   GPS module driver for AIR780EG CAT1 module
  * @note    AIR780EG GPS data is obtained via AT+CGNSINF query, not NMEA stream
  ******************************************************************************
  */

#include "GPS.h"
#include "GSM.h"
#include "main.h"  // 包含RTC相关定义
#include <string.h>
#include <stdlib.h>

// Global variables
GPS_Data_t gps_data;
Course_Data_t course_data;
uint8_t gps_data_ready = 0;
uint8_t gps_new_data = 0;
char gps_nmea_buffer[60]; // Store NMEA sentences to avoid circular overwrite

// GPS临时存储变量 - 保留用于兼容性，但不再使用
GPS_TempData_t gps_temp_data = {0};

// GPS module initialization
void GPS_Init(void)
{
    // Clear GPS data structure completely
    memset(&gps_data, 0, sizeof(GPS_Data_t));

    // Initialize with current RTC date/time instead of hardcoded defaults
    RTC_TimeTypeDef rtc_time;
    RTC_DateTypeDef rtc_date;
    HAL_RTC_GetTime(&hrtc, &rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &rtc_date, RTC_FORMAT_BIN);

    // Set GPS default values from current RTC (only time/date, not position)
    gps_data.year = 2000 + rtc_date.Year;
    gps_data.month = rtc_date.Month;
    gps_data.day = rtc_date.Date;
    gps_data.hour = rtc_time.Hours;
    gps_data.minute = rtc_time.Minutes;
    gps_data.second = rtc_time.Seconds;

    // 确保位置数据为0
    gps_data.latitude = 0.0;
    gps_data.longitude = 0.0;
    gps_data.latitude_decimal = 0.0;
    gps_data.longitude_decimal = 0.0;
    gps_data.altitude = 0.0;
    gps_data.fix_quality = 0;
    gps_data.satellites = 0;
    gps_data.speed = 0.0;
    gps_data.course = 0.0;
    gps_data.hdop = 0.0;
    gps_data.pdop = 0.0;
    gps_data.vdop = 0.0;
    gps_data.valid = 0;

    // Clear NMEA buffer
    memset(gps_nmea_buffer, 0, sizeof(gps_nmea_buffer));

    // Initialize flags
    gps_data_ready = 0;
    gps_new_data = 0;

    // Initialize course data
    GPS_CourseInit();

    // Initialize GPS temporary storage (保留用于兼容性)
    memset(&gps_temp_data, 0, sizeof(GPS_TempData_t));


}

// Initialize course data
void GPS_CourseInit(void)
{
    // Clear course data structure
    memset(&course_data, 0, sizeof(Course_Data_t));

    // Initialize course history data
    for (int i = 0; i < COURSE_HISTORY_SIZE; i++) {
        course_data.course_history[i] = 0.0f;
    }

    course_data.history_count = 0;
    course_data.history_index = 0;
    course_data.last_valid_course = 0.0f;
    course_data.has_valid_course = 0;
}

// Process NMEA data
void GPS_Process(char *nmea_data)
{
    if (GPS_ParseNMEA(nmea_data, &gps_data)) {
        gps_data.updated = 1;
    }
}

// Parse NMEA data
uint8_t GPS_ParseNMEA(char *nmea_data, GPS_Data_t *gps_data)
{
    char *token;
    char buffer[128];
    uint8_t field_count = 0;
    uint8_t result = 0;

    // 简化：使用临时有效性标志，避免GGA和RMC相互覆盖（参考副本版本）
    uint8_t gga_valid = 0;
    uint8_t rmc_valid = 0;

    // Check input parameter validity
    if (nmea_data == NULL || gps_data == NULL) {
        return 0;
    }

    // Copy data to buffer to avoid modifying original data
    strncpy(buffer, nmea_data, sizeof(buffer) - 1);
    buffer[sizeof(buffer) - 1] = '\0';

    // Get first field
    token = strtok(buffer, ",");
    if (token == NULL) {
        return 0;
    }

    // Parse GGA sentence - Global Positioning System fix data
    if (strcmp(token, "$GNGGA") == 0 || strcmp(token, "$GPGGA") == 0) {
        // Save original GGA sentence
        strncpy(gps_data->gga, nmea_data, sizeof(gps_data->gga) - 1);
        gps_data->gga[sizeof(gps_data->gga) - 1] = '\0';

        field_count = 1;
        while ((token = strtok(NULL, ",")) != NULL) {
            switch (field_count) {
                case 1: // Time
                    if (strlen(token) >= 6) {
                        gps_data->hour = (token[0] - '0') * 10 + (token[1] - '0');
                        gps_data->minute = (token[2] - '0') * 10 + (token[3] - '0');
                        gps_data->second = (token[4] - '0') * 10 + (token[5] - '0');
                        if (strlen(token) > 6 && token[6] == '.') {
                            gps_data->millisecond = atoi(&token[7]);
                        }
                    }
                    break;
                case 2: // Latitude
                    if (strlen(token) > 0) {
                        gps_data->latitude = atof(token);
                    }
                    break;
                case 3: // North/South hemisphere
                    if (strlen(token) > 0) {
                        gps_data->ns = token[0];
                    }
                    break;
                case 4: // Longitude
                    if (strlen(token) > 0) {
                        gps_data->longitude = atof(token);
                    }
                    break;
                case 5: // East/West hemisphere
                    if (strlen(token) > 0) {
                        gps_data->ew = token[0];
                    }
                    break;
                case 6: // Fix quality
                    if (strlen(token) > 0) {
                        gps_data->fix_quality = atoi(token);
                        // 简化：设置GGA有效性标志，但不直接修改gps_data->valid（参考副本版本）
                        if (gps_data->fix_quality > 0) {
                            gga_valid = 1;
                        } else {
                            gga_valid = 0;
                        }
                    }
                    break;
                case 7: // Number of satellites used
                    if (strlen(token) > 0) {
                        gps_data->satellites = atoi(token);
                    }
                    break;
                case 8: // Horizontal dilution of precision
                    if (strlen(token) > 0) {
                        gps_data->hdop = atof(token);
                    }
                    break;
                case 9: // Altitude
                    if (strlen(token) > 0) {
                        gps_data->altitude = atof(token);
                    }
                    break;
                default:
                    break;
            }
            field_count++;
        }

        // Calculate decimal degree format coordinates
        if (gps_data->latitude > 0 && gps_data->longitude > 0) {
            gps_data->latitude_decimal = GPS_ConvertToDecimalDegrees(gps_data->latitude, gps_data->ns);
            gps_data->longitude_decimal = GPS_ConvertToDecimalDegrees(gps_data->longitude, gps_data->ew);
            // GPS坐标转换调试信息
            #if GPS_DEBUG_ENABLE
            // printf("GPS: GGA coords converted - lat=%.6f, lon=%.6f\r\n",
            //        gps_data->latitude_decimal, gps_data->longitude_decimal);
            #endif
        }



        result = 1;
    }
    // Parse RMC sentence - Recommended minimum positioning information
    else if (strcmp(token, "$GNRMC") == 0 || strcmp(token, "$GPRMC") == 0) {
        // Save original RMC sentence
        strncpy(gps_data->rmc, nmea_data, sizeof(gps_data->rmc) - 1);
        gps_data->rmc[sizeof(gps_data->rmc) - 1] = '\0';

        field_count = 1;
        while ((token = strtok(NULL, ",*")) != NULL) {
            switch (field_count) {
                case 1: // Time
                    if (strlen(token) >= 6) {
                        gps_data->hour = (token[0] - '0') * 10 + (token[1] - '0');
                        gps_data->minute = (token[2] - '0') * 10 + (token[3] - '0');
                        gps_data->second = (token[4] - '0') * 10 + (token[5] - '0');
                        if (strlen(token) > 6 && token[6] == '.') {
                            gps_data->millisecond = atoi(&token[7]);
                        }
                    }
                    break;
                case 2: // Status A=valid, V=invalid
                    if (strlen(token) > 0) {
                        // 简化：设置RMC有效性标志（参考副本版本）
                        rmc_valid = (token[0] == 'A') ? 1 : 0;
                    }
                    break;
                case 3: // Latitude
                    if (strlen(token) > 0) {
                        gps_data->latitude = atof(token);
                    }
                    break;
                case 4: // North/South hemisphere
                    if (strlen(token) > 0) {
                        gps_data->ns = token[0];
                    }
                    break;
                case 5: // Longitude
                    if (strlen(token) > 0) {
                        gps_data->longitude = atof(token);
                    }
                    break;
                case 6: // East/West hemisphere
                    if (strlen(token) > 0) {
                        gps_data->ew = token[0];
                    }
                    break;
                case 7: // Speed (knots)
                    if (strlen(token) > 0) {
                        gps_data->speed = atof(token);
                    }
                    break;
                case 8: // Course (degrees)
                    if (strlen(token) > 0) {
                        gps_data->course = atof(token);
                    }
                    break;
                case 9: // Date (DDMMYY)
                    if (strlen(token) >= 6) {
                        // Parse date with validity check
                        uint8_t day = (token[0] - '0') * 10 + (token[1] - '0');
                        uint8_t month = (token[2] - '0') * 10 + (token[3] - '0');
                        uint16_t year = 2000 + (token[4] - '0') * 10 + (token[5] - '0');

                        // Only update GPS data when parsed date is valid
                        if (year >= 2020 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                            gps_data->day = day;
                            gps_data->month = month;
                            gps_data->year = year;
                        }
                        // If date is invalid, keep original values unchanged
                    }
                    break;
                default:
                    break;
            }
            field_count++;
        }

        // Calculate decimal degree format coordinates
        if (gps_data->latitude > 0 && gps_data->longitude > 0) {
            gps_data->latitude_decimal = GPS_ConvertToDecimalDegrees(gps_data->latitude, gps_data->ns);
            gps_data->longitude_decimal = GPS_ConvertToDecimalDegrees(gps_data->longitude, gps_data->ew);
        }

        result = 1;
    }

    // Parse GSA sentence - Satellite status
    else if (strcmp(token, "$GNGSA") == 0 || strcmp(token, "$GPGSA") == 0 || strcmp(token, "$BDGSA") == 0) {
        field_count = 1;
        while ((token = strtok(NULL, ",*")) != NULL) {
            // GSA sentence contains precision factor fields: PDOP, HDOP, VDOP
            if (field_count == 15) { // PDOP
                if (strlen(token) > 0) {
                    gps_data->pdop = atof(token);
                }
            } else if (field_count == 16) { // HDOP
                if (strlen(token) > 0) {
                    // Only update if GGA sentence didn't provide HDOP
                    if (gps_data->hdop == 0.0f) {
                        gps_data->hdop = atof(token);
                    }
                }
            } else if (field_count == 17) { // VDOP
                if (strlen(token) > 0) {
                    gps_data->vdop = atof(token);
                }
            }
            field_count++;
        }
        result = 1;
    }
    // Parse ZDA sentence - Date and time
    else if (strcmp(token, "$GNZDA") == 0 || strcmp(token, "$GPZDA") == 0) {
        field_count = 1;
        while ((token = strtok(NULL, ",*")) != NULL) {
            switch (field_count) {
                case 1: // Time
                    if (strlen(token) >= 6) {
                        gps_data->hour = (token[0] - '0') * 10 + (token[1] - '0');
                        gps_data->minute = (token[2] - '0') * 10 + (token[3] - '0');
                        gps_data->second = (token[4] - '0') * 10 + (token[5] - '0');
                        if (strlen(token) > 6 && token[6] == '.') {
                            gps_data->millisecond = atoi(&token[7]);
                        }
                    }
                    break;
                case 2: // Day
                    if (strlen(token) > 0) {
                        gps_data->day = atoi(token);
                    }
                    break;
                case 3: // Month
                    if (strlen(token) > 0) {
                        gps_data->month = atoi(token);
                    }
                    break;
                case 4: // Year
                    if (strlen(token) > 0) {
                        gps_data->year = atoi(token);
                    }
                    break;
                default:
                    break;
            }
            field_count++;
        }
        result = 1;
    }

    // 修正：统一计算最终的有效性状态
    // 1. 如果有经纬度数据，则认为数据有效
    // 2. 如果GGA或RMC有效，则认为数据有效
    if (gps_data->latitude > 0 && gps_data->longitude > 0) {
        gps_data->valid = 1;
        // GPS有效性判断调试信息
        #if GPS_DEBUG_ENABLE
        // printf("GPS: Position valid by coordinates, lat=%.6f, lon=%.6f\r\n",
        //        gps_data->latitude_decimal, gps_data->longitude_decimal);
        #endif
    } else if (gga_valid) {
        gps_data->valid = 1;
        #if GPS_DEBUG_ENABLE
        // printf("GPS: GGA valid, quality=%d, sats=%d, lat=%.6f, lon=%.6f\r\n",
        //        gps_data->fix_quality, gps_data->satellites,
        //        gps_data->latitude_decimal, gps_data->longitude_decimal);
        #endif
    } else if (rmc_valid) {
        gps_data->valid = 1;
        #if GPS_DEBUG_ENABLE
        // printf("GPS: RMC valid, lat=%.6f, lon=%.6f\r\n",
        //        gps_data->latitude_decimal, gps_data->longitude_decimal);
        #endif
    } else {
        gps_data->valid = 0;
        #if GPS_DEBUG_ENABLE
        // printf("GPS: Invalid - gga_valid=%d, rmc_valid=%d, quality=%d\r\n",
        //        gga_valid, rmc_valid, gps_data->fix_quality);
        #endif
    }

    return result;
}

// Convert NMEA format coordinates to decimal degrees format
float GPS_ConvertToDecimalDegrees(float nmea_format, char direction)
{
    int degrees = (int)(nmea_format / 100);
    float minutes = nmea_format - degrees * 100;
    float decimal_degrees = degrees + minutes / 60.0f;

    // Southern and western hemispheres are negative
    if (direction == 'S' || direction == 'W') {
        decimal_degrees = -decimal_degrees;
    }

    return decimal_degrees;
}

// Print GPS information
void GPS_PrintInfo(GPS_Data_t *gps_data)
{
    if (gps_data == NULL) {
        return;
    }

    // 函数体为空，调试输出已移除
}

// Synchronize RTC clock
uint8_t GPS_SyncRTC(GPS_Data_t *gps_data)
{
    if (gps_data == NULL || !gps_data->valid) {
        return 0;
    }

    // Smart GPS time sync: when GPS has positioning info, time is definitely valid
    // Check if time is valid
    uint8_t time_valid = (gps_data->hour <= 23 && gps_data->minute <= 59 && gps_data->second <= 59);
    if (!time_valid) {
        return 0;
    }

    // Check if date is valid
    uint8_t date_valid = (gps_data->year >= 2020 && gps_data->month >= 1 &&
                         gps_data->month <= 12 && gps_data->day >= 1 && gps_data->day <= 31);
    if (!date_valid) {
        // GPS date invalid but time valid, sync time only, keep RTC date
        RTC_TimeTypeDef rtc_time;
        RTC_DateTypeDef rtc_date;
        HAL_RTC_GetTime(&hrtc, &rtc_time, RTC_FORMAT_BIN);
        HAL_RTC_GetDate(&hrtc, &rtc_date, RTC_FORMAT_BIN);

        return RTC_SetDateTime(gps_data->hour, gps_data->minute, gps_data->second,
                              rtc_date.Date, rtc_date.Month, rtc_date.Year) == HAL_OK ? 1 : 0;
    }

    // GPS time and date both valid, perform complete sync
    if (RTC_SetDateTime(gps_data->hour, gps_data->minute, gps_data->second,
                        gps_data->day, gps_data->month, gps_data->year % 100) == HAL_OK) {
        return 1;
    } else {
        return 0;
    }
}

// Get position string for data transmission
char* GPS_GetPositionString(GPS_Data_t *gps_data, char *buffer)
{
    if (gps_data == NULL || buffer == NULL || !gps_data->valid) {
        return NULL;
    }

    // Format position information as string, suitable for GM20 module transmission
    sprintf(buffer, "+%.5f+%.5f+%.1f+%d+%.2f",
            gps_data->latitude,
            gps_data->longitude,
            gps_data->altitude,
            gps_data->satellites,
            gps_data->hdop);

    return buffer;
}

// Process course data
float GPS_ProcessCourse(float course, float speed_kmh)
{
    // If speed is below threshold, use last valid course
    if (speed_kmh < COURSE_SPEED_THRESHOLD) {
        return course_data.has_valid_course ? course_data.last_valid_course : 0.0f;
    }

    // Speed above threshold, current course is valid
    course_data.has_valid_course = 1;
    course_data.last_valid_course = course;

    // Add to history data
    course_data.course_history[course_data.history_index] = course;
    course_data.history_index = (course_data.history_index + 1) % COURSE_HISTORY_SIZE;
    if (course_data.history_count < COURSE_HISTORY_SIZE) {
        course_data.history_count++;
    }

    // Return smoothed course
    return GPS_GetSmoothedCourse();
}

// Get smoothed course value
float GPS_GetSmoothedCourse(void)
{
    // If not enough history data, return last valid course directly
    if (course_data.history_count < 2) {
        return course_data.last_valid_course;
    }

    // Calculate course average (need special handling for angle circularity)
    float sin_sum = 0.0f;
    float cos_sum = 0.0f;

    for (int i = 0; i < course_data.history_count; i++) {
        // Convert angle to radians
        float angle_rad = course_data.course_history[i] * 3.14159f / 180.0f;
        sin_sum += sinf(angle_rad);
        cos_sum += cosf(angle_rad);
    }

    // Calculate average direction (radians)
    float avg_angle_rad = atan2f(sin_sum, cos_sum);

    // Convert back to degrees, ensure in 0-360 range
    float avg_angle_deg = avg_angle_rad * 180.0f / 3.14159f;
    if (avg_angle_deg < 0) {
        avg_angle_deg += 360.0f;
    }

    return avg_angle_deg;
}

// Handle new GPS data
void GPS_HandleNewData(const char *nmea_data)
{
    if (nmea_data == NULL || strlen(nmea_data) < 10 || nmea_data[0] != '$') {
        return;
    }

    // Copy NMEA sentence to global buffer
    strcpy(gps_nmea_buffer, nmea_data);
    gps_new_data = 1;  // Set new data flag

    // Process GPS data (cast const away for compatibility)
    GPS_Process((char*)nmea_data);

    // Set data ready flag
    gps_data_ready = 1;
}

// Update GPS data (process course)
void GPS_UpdateData(void)
{
    if (gps_new_data && gps_data.valid) {
        // Process course data
        float speed_kmh = gps_data.speed * 1.852f;
        float processed_course = GPS_ProcessCourse(gps_data.course, speed_kmh);

        // Update course value in GPS data structure to processed value
        gps_data.course = processed_course;

        // Clear new data flag
        gps_new_data = 0;
    }
}

// Check if GPS data is ready
uint8_t GPS_IsDataReady(void)
{
    return gps_data_ready;
}

// Clear GPS data ready flag
void GPS_ClearDataReadyFlag(void)
{
    gps_data_ready = 0;
    gps_new_data = 0;
}

// Check if quality timeout has occurred (保留用于兼容性，但不再使用)
uint8_t GPS_CheckQualityTimeout(uint32_t timeout_ms)
{
    // 简化版本：不再使用临时数据机制
    return 0; // No timeout
}

// Use temporary data if available (保留用于兼容性，但不再使用)
void GPS_UseTempDataIfNeeded(void)
{
    // 简化版本：不再使用临时数据机制
}


/**
 * @brief 简单的字符串转浮点数函数（替代atof）
 * @param str 字符串
 * @return float 浮点数
 */
static float simple_atof(const char* str)
{
    if (!str || *str == '\0') return 0.0f;

    float result = 0.0f;
    float fraction = 0.0f;
    int divisor = 1;
    int sign = 1;
    int decimal_found = 0;

    // 处理符号
    if (*str == '-') {
        sign = -1;
        str++;
    } else if (*str == '+') {
        str++;
    }

    // 解析数字
    while (*str) {
        if (*str >= '0' && *str <= '9') {
            if (decimal_found) {
                fraction = fraction * 10 + (*str - '0');
                divisor *= 10;
            } else {
                result = result * 10 + (*str - '0');
            }
        } else if (*str == '.' && !decimal_found) {
            decimal_found = 1;
        } else {
            break; // 遇到非数字字符停止
        }
        str++;
    }

    return sign * (result + fraction / divisor);
}

/**
 * @brief 通过AT+CGNSINF查询并解析AIR780EG GPS数据
 * @return GPS_Status_t 查询状态
 */
GPS_Status_t GPS_QueryAIR780EG(void)
{
    char gps_response[512] = {0};

    // 发送AT+CGNSINF查询GPS信息，完全阻塞接收，等待超时后查看数据，这里不要修改，这个逻辑是正确的
    // printf("GPS: Sending AT+CGNSINF...\r\n");
    gsm_status_t status = gsm_send_at_command("AT+CGNSINF", gps_response, 300);

    if (status != GSM_OK) {
        // printf("GPS: AT+CGNSINF failed\r\n");
        return GPS_ERROR;
    }

    // 原始数据接收完成

    // 查找+CGNSINF行并解析
    char *cgnsinf_line = strstr(gps_response, "+CGNSINF:");
    if (!cgnsinf_line) {
        return GPS_ERROR;
    }

    // 截断到行结束
    char *line_end = strstr(cgnsinf_line, "\r");
    if (!line_end) line_end = strstr(cgnsinf_line, "\n");
    if (line_end) *line_end = '\0';

    // 跳过"+CGNSINF: "
    char *data = cgnsinf_line + 10;

    // 手动解析字段
    char *tokens[25];
    int token_count = 0;
    char *start = data;
    char *pos = data;

    while (*pos && token_count < 25) {
        if (*pos == ',') {
            *pos = '\0';
            tokens[token_count++] = start;
            start = pos + 1;
        }
        pos++;
    }
    if (token_count < 25) {
        tokens[token_count++] = start;
    }

    if (token_count >= 16) {
        int run = atoi(tokens[0]);
        int fix = atoi(tokens[1]);
        char *utc = tokens[2];

        // 更新GPS数据
        gps_data.valid = (run == 1 && fix == 1);

        // 解析UTC时间
        if (strlen(utc) >= 14) {
            gps_data.year = (utc[0]-'0')*1000 + (utc[1]-'0')*100 + (utc[2]-'0')*10 + (utc[3]-'0');
            gps_data.month = (utc[4]-'0')*10 + (utc[5]-'0');
            gps_data.day = (utc[6]-'0')*10 + (utc[7]-'0');
            gps_data.hour = (utc[8]-'0')*10 + (utc[9]-'0');
            gps_data.minute = (utc[10]-'0')*10 + (utc[11]-'0');
            gps_data.second = (utc[12]-'0')*10 + (utc[13]-'0');
        }

        // 解析位置信息
        if (gps_data.valid && token_count >= 19) {
            // 使用自定义的simple_atof函数替代atof
            gps_data.latitude_decimal = simple_atof(tokens[3]);
            gps_data.longitude_decimal = simple_atof(tokens[4]);
            gps_data.altitude = simple_atof(tokens[5]);
            gps_data.speed = simple_atof(tokens[6]);
            gps_data.course = simple_atof(tokens[7]);
            gps_data.hdop = simple_atof(tokens[10]);
            gps_data.pdop = simple_atof(tokens[11]);
            gps_data.vdop = simple_atof(tokens[12]);
            // 使用参与定位的卫星数量（字段16），而不是扫描到的总数
            gps_data.satellites = atoi(tokens[15]);
            gps_data.fix_quality = fix;
            gps_data.ns = (gps_data.latitude_decimal >= 0) ? 'N' : 'S';
            gps_data.ew = (gps_data.longitude_decimal >= 0) ? 'E' : 'W';

            // 存储CN0信号强度（字段18）
            gps_data.cn0_max = (token_count > 18) ? atoi(tokens[18]) : 0;
        } else {
            // 使用参与定位的卫星数量（字段16），而不是扫描到的总数
            gps_data.satellites = (token_count > 15 ? atoi(tokens[15]) : 0);
            gps_data.cn0_max = 0;
        }
    }

    // 基于卫星数量重新评估GPS有效性
    if (gps_data.valid && gps_data.satellites < MIN_SATELLITES_FOR_FIX) {
        gps_data.valid = 0;  // 卫星数量不足，标记为无效
        // printf("GPS: Insufficient satellites (%d < %d), marking as invalid\r\n",
        //        gps_data.satellites, MIN_SATELLITES);
    }

    // 设置数据就绪标志
    gps_data_ready = 1;
    gps_new_data = 1;

    return GPS_OK;  // 添加这一行
}

// ==================== 正式应用函数 ====================

/**
 * @brief 更新GPS数据并显示详细信息
 * @return GPS_Status_t 操作状态
 */
GPS_Status_t GPS_UpdateAndDisplay(void)
{
    GPS_Status_t status = GPS_QueryAIR780EG();

    if (status == GPS_OK && GPS_IsDataReady()) {
        GPS_PrintDetailedInfo();
        GPS_ClearDataReadyFlag();
    }

    return status;
}

/**
 * @brief 获取当前GPS位置信息
 * @param latitude 纬度指针
 * @param longitude 经度指针
 * @param altitude 海拔指针
 * @return GPS_Status_t 操作状态
 */
GPS_Status_t GPS_GetCurrentPosition(float *latitude, float *longitude, float *altitude)
{
    if (!latitude || !longitude || !altitude) {
        return GPS_ERROR;
    }

    if (!gps_data.valid) {
        return GPS_NO_FIX;
    }

    *latitude = gps_data.latitude_decimal;
    *longitude = gps_data.longitude_decimal;
    *altitude = gps_data.altitude;

    return GPS_OK;
}

/**
 * @brief 检查GPS位置是否有效
 * @return uint8_t 1=有效, 0=无效
 */
uint8_t GPS_IsPositionValid(void)
{
    return gps_data.valid;
}

/**
 * @brief 打印详细的GPS信息（包含信号质量评估）
 */
void GPS_PrintDetailedInfo(void)
{
    // printf("=== GPS Information ===\r\n");
    // printf("GPS Status: %s\r\n", gps_data.valid ? "Fixed" : "Searching");

    if (gps_data.valid) {  // 有效定位
        // printf("UTC: %04d-%02d-%02d %02d:%02d:%02d\r\n",
        //        gps_data.year, gps_data.month, gps_data.day,
        //        gps_data.hour, gps_data.minute, gps_data.second);
        // printf("Latitude: %.6f %c\r\n", gps_data.latitude_decimal, gps_data.ns);
        // printf("Longitude: %.6f %c\r\n", gps_data.longitude_decimal, gps_data.ew);
        // printf("Altitude: %.1f m\r\n", gps_data.altitude);
        // printf("Speed: %.2f km/h\r\n", gps_data.speed);
        // printf("Course: %.1f deg\r\n", gps_data.course);
        // printf("Satellites: %d\r\n", gps_data.satellites);
        // printf("HDOP: %.2f, PDOP: %.2f, VDOP: %.2f\r\n",
        //        gps_data.hdop, gps_data.pdop, gps_data.vdop);

        // 调用信号质量评估
        GPS_EvaluateSignalQuality();
    } else {
        // printf("Searching satellites... (Count: %d)\r\n", gps_data.satellites);
        // printf("UTC: %04d-%02d-%02d %02d:%02d:%02d\r\n",
        //        gps_data.year, gps_data.month, gps_data.day,
        //        gps_data.hour, gps_data.minute, gps_data.second);
    }
    // printf("=======================\r\n");
}

/**
 * @brief 评估GPS信号质量并输出警告信息
 */
void GPS_EvaluateSignalQuality(void)
{
    // 信号强度评估
    // printf("Signal Strength: %d dBHz", gps_data.cn0_max);
    if (gps_data.cn0_max >= 45) {
        // printf(" (Excellent)\r\n");
    } else if (gps_data.cn0_max >= 40) {
        // printf(" (Good)\r\n");
    } else if (gps_data.cn0_max >= 35) {
        // printf(" (Fair)\r\n");
    } else if (gps_data.cn0_max >= 30) {
        // printf(" (Weak)\r\n");
    } else {
        // printf(" (Very Weak)\r\n");
    }

    // 精度警告和信号质量评估
    if (gps_data.hdop > 20.0) {
        // printf("WARNING: Very poor accuracy (HDOP > 20) - Move to open area\r\n");
    } else if (gps_data.hdop > 5.0) {
        // printf("WARNING: Poor horizontal accuracy (HDOP > 5.0)\r\n");
    }
    if (gps_data.pdop > 10.0) {
        // printf("WARNING: Very poor position accuracy (PDOP > 10)\r\n");
    } else if (gps_data.pdop > 3.0) {
        // printf("WARNING: Poor position accuracy (PDOP > 3.0)\r\n");
    }

    // 卫星几何分析
    if (gps_data.satellites >= 12 && gps_data.hdop > 10.0) {
        // printf("INFO: Many satellites but poor geometry - Try different location\r\n");
    } else if (gps_data.satellites < 8) {
        // printf("INFO: Low satellite count (%d) - Signal may be unstable\r\n", gps_data.satellites);
    }
}

/**
 * @brief 生成HY协议数据包
 * @param master_battery 主设备电池电压(mV)
 * @param slave_id 子设备ID
 * @param slave_battery 子设备电池电压(mV)
 */
void GPS_GenerateHYDataPacket(uint16_t master_battery, uint16_t slave_id, uint16_t slave_battery)
{
    // printf("=== GPS Data Package Phase ===\r\n");
    // printf("Building HY data packet...\r\n");

    // 构建HY数据包
    char data_packet[256];  // 使用较小的缓冲区
    char temp_str[32];      // 使用较小的临时字符串

    // 开始构建数据包
    strcpy(data_packet, "HY");

    // 添加长度占位符（稍后计算）
    strcat(data_packet, "000");

    // 添加标识（S=实时数据）
    strcat(data_packet, "S");

    // 添加经度
    if (gps_data.valid) {
        sprintf(temp_str, "+%.6f", gps_data.longitude_decimal);
    } else {
        sprintf(temp_str, "+000.000000");
    }
    strcat(data_packet, temp_str);

    // 添加纬度
    if (gps_data.valid) {
        sprintf(temp_str, "+%.6f", gps_data.latitude_decimal);
    } else {
        sprintf(temp_str, "+00.000000");
    }
    strcat(data_packet, temp_str);

    // 添加UTC时间（YYYYMMDDHHMMSS格式）
    if (gps_data.valid) {
        // GPS定位成功，使用GPS的UTC时间
        sprintf(temp_str, "+%04d%02d%02d%02d%02d%02d",
                gps_data.year, gps_data.month, gps_data.day,
                gps_data.hour, gps_data.minute, gps_data.second);
    } else {
        // GPS定位失败，使用RTC时间
        printf("GPS invalid, trying to use RTC time...\r\n");
        extern RTC_HandleTypeDef hrtc;  // 声明外部RTC句柄
        RTC_TimeTypeDef sTime;
        RTC_DateTypeDef sDate;

        // 必须先获取时间，再获取日期（STM32 RTC要求）
        HAL_StatusTypeDef time_status = HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
        HAL_StatusTypeDef date_status = HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);

        printf("RTC status: time=%d, date=%d\r\n", time_status, date_status);

        if (time_status == HAL_OK && date_status == HAL_OK) {
            // RTC时间有效，使用RTC时间
            printf("Using RTC time in packet: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
                   sDate.Year, sDate.Month, sDate.Date,
                   sTime.Hours, sTime.Minutes, sTime.Seconds);
            sprintf(temp_str, "+20%02d%02d%02d%02d%02d%02d",
                    sDate.Year, sDate.Month, sDate.Date,
                    sTime.Hours, sTime.Minutes, sTime.Seconds);
        } else {
            // RTC时间也无效，使用默认时间
            printf("RTC time invalid, using default time\r\n");
            sprintf(temp_str, "+00000000000000");
        }
    }
    strcat(data_packet, temp_str);

    // 添加海拔
    if (gps_data.valid) {
        sprintf(temp_str, "+%.1f", gps_data.altitude);
    } else {
        sprintf(temp_str, "+0.0");
    }
    strcat(data_packet, temp_str);

    // 添加GPS状态
    sprintf(temp_str, "+%d", gps_data.valid ? 1 : 0);
    strcat(data_packet, temp_str);

    // 添加卫星数量
    sprintf(temp_str, "+%d", gps_data.satellites);
    strcat(data_packet, temp_str);

    // 添加速度
    if (gps_data.valid) {
        sprintf(temp_str, "+%.2f", gps_data.speed);
    } else {
        sprintf(temp_str, "+0.00");
    }
    strcat(data_packet, temp_str);

    // 添加电池电压
    sprintf(temp_str, "+%.2f", master_battery / 1000.0f);
    strcat(data_packet, temp_str);

    // 添加HDOP
    if (gps_data.valid) {
        sprintf(temp_str, "+%.2f", gps_data.hdop);
    } else {
        sprintf(temp_str, "+99.99");
    }
    strcat(data_packet, temp_str);

    // 添加PDOP
    if (gps_data.valid) {
        sprintf(temp_str, "+%.2f", gps_data.pdop);
    } else {
        sprintf(temp_str, "+99.99");
    }
    strcat(data_packet, temp_str);

    // 添加方位角
    if (gps_data.valid) {
        sprintf(temp_str, "+%.1f", gps_data.course);
    } else {
        sprintf(temp_str, "+0.0");
    }
    strcat(data_packet, temp_str);

    // 添加子设备ID（RTC唤醒时使用0000）
    if (slave_id == 0) {
        sprintf(temp_str, "+0000");  // RTC唤醒时的默认值
    } else {
        sprintf(temp_str, "+%04X", slave_id);  // 正常的子设备ID
    }
    strcat(data_packet, temp_str);

    // 添加子设备电压（RTC唤醒时使用0.0）
    if (slave_battery == 0) {
        sprintf(temp_str, "+0.0");  // RTC唤醒时的默认值
    } else {
        sprintf(temp_str, "+%.1f", slave_battery / 1000.0f);  // 正常的子设备电压
    }
    strcat(data_packet, temp_str);

    // 添加GSM信号强度（使用全局变量）
    sprintf(temp_str, "+%d", global_signal_dbm);
    strcat(data_packet, temp_str);

    // 添加ICCID（使用全局变量）
    sprintf(temp_str, "+%s", global_ccid);
    strcat(data_packet, temp_str);

    // 添加结束标识
    strcat(data_packet, "+E");

    // 计算并更新长度（去掉HY和长度字段本身）
    int data_length = strlen(data_packet) - 5;  // 减去"HY000"
    sprintf(temp_str, "%03d", data_length);
    memcpy(data_packet + 2, temp_str, 3);  // 更新长度字段

    // 输出完整的数据包
//    printf("=== HY Data Packet Complete ===\r\n");
    printf("%s\r\n", data_packet);
//    printf("Packet Length: %d bytes\r\n", strlen(data_packet));
//    printf("Data Payload: %d bytes\r\n", data_length);
//    printf("===============================\r\n");
}

/**
 * @brief 生成HY协议数据包并返回字符串
 * @param master_battery 主设备电池电压(mV)
 * @param slave_id 子设备ID
 * @param slave_battery 子设备电池电压(mV)
 * @param buffer 存储数据包的缓冲区
 * @return 返回数据包字符串指针
 */
char* GPS_GenerateHYDataPacketString(uint16_t master_battery, uint16_t slave_id, uint16_t slave_battery, char* buffer)
{
    if (buffer == NULL) {
        return NULL;
    }

    char temp_str[32];      // 临时字符串

    // 开始构建数据包
    strcpy(buffer, "HY");

    // 添加长度占位符（稍后计算）
    strcat(buffer, "000");

    // 添加标识（S=实时数据）
    strcat(buffer, "S");

    // 添加经度
    if (gps_data.valid) {
        sprintf(temp_str, "+%.6f", gps_data.longitude_decimal);
    } else {
        sprintf(temp_str, "+000.000000");
    }
    strcat(buffer, temp_str);

    // 添加纬度
    if (gps_data.valid) {
        sprintf(temp_str, "+%.6f", gps_data.latitude_decimal);
    } else {
        sprintf(temp_str, "+00.000000");
    }
    strcat(buffer, temp_str);

    // 添加UTC时间（YYYYMMDDHHMMSS格式）
    if (gps_data.valid) {
        // GPS定位成功，使用GPS的UTC时间
        sprintf(temp_str, "+%04d%02d%02d%02d%02d%02d",
                gps_data.year, gps_data.month, gps_data.day,
                gps_data.hour, gps_data.minute, gps_data.second);
    } else {
        // GPS定位失败，使用RTC时间
        extern RTC_HandleTypeDef hrtc;  // 声明外部RTC句柄
        RTC_TimeTypeDef sTime;
        RTC_DateTypeDef sDate;

        // 必须先获取时间，再获取日期（STM32 RTC要求）
        HAL_StatusTypeDef time_status = HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
        HAL_StatusTypeDef date_status = HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);

        if (time_status == HAL_OK && date_status == HAL_OK) {
            // RTC时间有效，使用RTC时间
            printf("Using RTC time: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
                   sDate.Year, sDate.Month, sDate.Date,
                   sTime.Hours, sTime.Minutes, sTime.Seconds);
            sprintf(temp_str, "+20%02d%02d%02d%02d%02d%02d",
                    sDate.Year, sDate.Month, sDate.Date,
                    sTime.Hours, sTime.Minutes, sTime.Seconds);
        } else {
            // RTC时间也无效，使用默认时间
            printf("RTC time invalid, using default time\r\n");
            sprintf(temp_str, "+00000000000000");
        }
    }
    strcat(buffer, temp_str);

    // 添加海拔
    if (gps_data.valid) {
        sprintf(temp_str, "+%.1f", gps_data.altitude);
    } else {
        sprintf(temp_str, "+0.0");
    }
    strcat(buffer, temp_str);

    // 添加GPS状态
    sprintf(temp_str, "+%d", gps_data.valid ? 1 : 0);
    strcat(buffer, temp_str);

    // 添加卫星数量
    sprintf(temp_str, "+%d", gps_data.satellites);
    strcat(buffer, temp_str);

    // 添加速度
    if (gps_data.valid) {
        sprintf(temp_str, "+%.2f", gps_data.speed);
    } else {
        sprintf(temp_str, "+0.00");
    }
    strcat(buffer, temp_str);

    // 添加电池电压
    sprintf(temp_str, "+%.2f", master_battery / 1000.0f);
    strcat(buffer, temp_str);

    // 添加HDOP
    if (gps_data.valid) {
        sprintf(temp_str, "+%.2f", gps_data.hdop);
    } else {
        sprintf(temp_str, "+99.99");
    }
    strcat(buffer, temp_str);

    // 添加PDOP
    if (gps_data.valid) {
        sprintf(temp_str, "+%.2f", gps_data.pdop);
    } else {
        sprintf(temp_str, "+99.99");
    }
    strcat(buffer, temp_str);

    // 添加方位角
    if (gps_data.valid) {
        sprintf(temp_str, "+%.1f", gps_data.course);
    } else {
        sprintf(temp_str, "+0.0");
    }
    strcat(buffer, temp_str);

    // 添加子设备ID（RTC唤醒时使用0000）
    if (slave_id == 0) {
        sprintf(temp_str, "+0000");  // RTC唤醒时的默认值
    } else {
        sprintf(temp_str, "+%04X", slave_id);  // 正常的子设备ID
    }
    strcat(buffer, temp_str);

    // 添加子设备电压（RTC唤醒时使用0.0）
    if (slave_battery == 0) {
        sprintf(temp_str, "+0.0");  // RTC唤醒时的默认值
    } else {
        sprintf(temp_str, "+%.1f", slave_battery / 1000.0f);  // 正常的子设备电压
    }
    strcat(buffer, temp_str);

    // 添加GSM信号强度（使用全局变量）
    sprintf(temp_str, "+%d", global_signal_dbm);
    strcat(buffer, temp_str);

    // 添加ICCID（使用全局变量）
    sprintf(temp_str, "+%s", global_ccid);
    strcat(buffer, temp_str);

    // 添加结束标识
    strcat(buffer, "+E");

    // 计算并更新长度（去掉HY和长度字段本身）
    int data_length = strlen(buffer) - 5;  // 减去"HY000"
    sprintf(temp_str, "%03d", data_length);
    memcpy(buffer + 2, temp_str, 3);  // 更新长度字段

    return buffer;
}


